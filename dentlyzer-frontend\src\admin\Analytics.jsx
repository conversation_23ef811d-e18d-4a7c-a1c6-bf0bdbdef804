import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AdminSidebar from './AdminSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import {
  FaChartLine,
  FaCalendarAlt,
  FaStar,
  FaUserAlt,
  FaUsers,
  FaUserGraduate,
  FaUserNurse,
  FaUserMd,
  FaClipboardCheck,
  FaChartPie,
  FaChartBar,
  FaChartArea,
  FaFileMedical
} from 'react-icons/fa';
import { Bar, Pie, Doughnut, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
} from 'chart.js';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
);

// Define a consistent color palette based on the website design system
const colorPalette = {
  primary: { main: `${websiteColorPalette.primary}CC`, border: websiteColorPalette.primary },
  secondary: { main: `${websiteColorPalette.secondary}CC`, border: websiteColorPalette.secondary },
  accent: { main: `${websiteColorPalette.accent}CC`, border: websiteColorPalette.accent },
  red: { main: 'rgba(239, 68, 68, 0.8)', border: 'rgba(239, 68, 68, 1)' },
  orange: { main: 'rgba(249, 115, 22, 0.8)', border: 'rgba(249, 115, 22, 1)' },
  amber: { main: 'rgba(245, 158, 11, 0.8)', border: 'rgba(245, 158, 11, 1)' },
};

const Analytics = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [appointments, setAppointments] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [patients, setPatients] = useState([]);
  const [students, setStudents] = useState([]);
  const [supervisors, setSupervisors] = useState([]);
  const [assistants, setAssistants] = useState([]);
  const [treatmentSheets, setTreatmentSheets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !token) {
        setError('Please log in to view analytics.');
        setLoading(false);
        return;
      }

      if (!user.university) {
        setError('User profile incomplete. Missing university information.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };

        // First, get students, supervisors, and other data
        const initialRequests = [
          axios.get(`http://localhost:5000/api/admin/appointments?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`http://localhost:5000/api/admin/patients?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`http://localhost:5000/api/admin/students?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`http://localhost:5000/api/admin/supervisors?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`http://localhost:5000/api/admin/assistants?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`http://localhost:5000/api/admin/treatment-sheets`, config)
        ];

        // Execute initial requests and handle errors individually
        const [appointmentsRes, patientsRes, studentsRes, supervisorsRes, assistantsRes, treatmentSheetsRes] = await Promise.all(
          initialRequests.map(request =>
            request.catch(error => {
              console.error('API request failed:', error.message);
              return { data: [] }; // Return empty data on error
            })
          )
        );

        const students = studentsRes.data || [];
        console.log(`Found ${students.length} students in university ${user.university}`);

        // Now fetch reviews for each student in the university
        let allReviews = [];

        if (students.length > 0) {
          console.log('Fetching reviews for students...');

          // Create an array of promises for fetching reviews for each student
          const reviewPromises = students.map(student => {
            const url = `http://localhost:5000/api/reviews/student?studentId=${student.studentId}`;
            console.log(`Fetching reviews for student ${student.name} with URL: ${url}`);

            return axios.get(url, config)
              .then(response => {
                console.log(`Successfully fetched ${response.data.length} reviews for student ${student.name}`);
                return response;
              })
              .catch(error => {
                console.error(`Error fetching reviews for student ${student.name}:`, error.message);
                // Try with _id as fallback
                return axios.get(`http://localhost:5000/api/reviews/student?studentId=${student._id}`, config)
                  .then(response => {
                    console.log(`Fallback successful! Fetched ${response.data.length} reviews for student ${student.name}`);
                    return response;
                  })
                  .catch(fallbackError => {
                    console.error(`Fallback also failed for student ${student.name}:`, fallbackError.message);
                    return { data: [] }; // Return empty array on error
                  });
              });
          });

          // Execute all promises in parallel
          const reviewsResults = await Promise.all(reviewPromises);

          // Combine all reviews into a single array
          reviewsResults.forEach(result => {
            if (result.data && Array.isArray(result.data)) {
              // Filter out storage signature from reviews
              const filteredReviews = result.data.map(review => {
                // Create a new object without the signature property
                const { signature, ...reviewWithoutSignature } = review;
                return reviewWithoutSignature;
              });

              allReviews = [...allReviews, ...filteredReviews];
            }
          });

          // Remove any duplicate reviews (in case a student appears in multiple queries)
          allReviews = Array.from(new Map(allReviews.map(review => [review._id, review])).values());

          console.log(`Found ${allReviews.length} total reviews for students in university ${user.university}`);
        }

        setAppointments(appointmentsRes.data || []);
        setReviews(allReviews);
        setPatients(patientsRes.data || []);
        setStudents(students);
        setSupervisors(supervisorsRes.data || []);
        setAssistants(assistantsRes.data || []);
        setTreatmentSheets(treatmentSheetsRes.data || []);

        if (
          appointmentsRes.data.length === 0 &&
          allReviews.length === 0 &&
          patientsRes.data.length === 0 &&
          treatmentSheetsRes.data.length === 0
        ) {
          setError('No data found for analytics.');
        }
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage =
          err.response?.status === 404
            ? 'Analytics endpoints not found.'
            : err.response?.status === 401
            ? 'Unauthorized. Please log in again.'
            : err.response?.data?.message || 'Failed to load analytics';
        setError(errorMessage);
        if (err.response?.status === 401) navigate('/login');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user, token, navigate]);

  // Process appointments data
  const appointmentsAnalytics = {
    total: appointments.length,
    pending: appointments.filter((a) => a.status === 'pending').length,
    completed: appointments.filter((a) => a.status === 'completed').length,
    cancelled: appointments.filter((a) => a.status === 'cancelled').length,
    today: appointments.filter((a) => {
      const apptDate = new Date(a.date);
      const todayDate = new Date();
      return apptDate.toDateString() === todayDate.toDateString();
    }).length,
    thisWeek: appointments.filter((a) => {
      const apptDate = new Date(a.date);
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // End of week (Saturday)
      return apptDate >= startOfWeek && apptDate <= endOfWeek;
    }).length,
    byMonth: getAppointmentsByMonth(appointments),
    byDay: getAppointmentsByDay(appointments),
    byType: getAppointmentsByType(appointments)
  };

  // Filter out signature storage reviews
  const filteredReviews = reviews.filter(review =>
    !(review.patientId && review.patientId.nationalId === 'signature-storage')
  );

  // Process reviews data
  const reviewsAnalytics = {
    total: filteredReviews.length,
    pending: filteredReviews.filter(r => r.status === 'pending').length,
    accepted: filteredReviews.filter(r => r.status === 'accepted').length,
    declined: filteredReviews.filter(r => r.status === 'declined' || r.status === 'denied').length,
    avgProcedureQuality: calculateAverageRating(filteredReviews, 'procedureQuality'),
    avgPatientInteraction: calculateAverageRating(filteredReviews, 'patientInteraction'),
    byProcedureType: getReviewsByProcedureType(filteredReviews),
    byMonth: getReviewsByMonth(filteredReviews),
    byStudent: getReviewsByStudent(filteredReviews)
  };

  console.log('Reviews analytics:', reviewsAnalytics);

  // Process patients data
  const patientsAnalytics = {
    total: patients.length,
    byAge: getPatientsAgeDistribution(patients),
    byGender: getPatientsGenderDistribution(patients),
    byChronicDisease: getPatientsChronicDiseaseDistribution(patients)
  };

  // People analytics
  const peopleAnalytics = {
    students: students.length,
    supervisors: supervisors.length,
    assistants: assistants.length,
    patients: patients.length
  };

  // Process treatment sheets data
  const proceduresAnalytics = {
    total: treatmentSheets.length,
    byType: getProceduresByType(treatmentSheets),
    byMonth: getProceduresByMonth(treatmentSheets),
    byStudent: getProceduresByStudent(treatmentSheets),
    byPatient: getProceduresByPatient(treatmentSheets),
    recentSheets: treatmentSheets
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 10)
  };

  // Chart data for overview
  const overviewChartData = {
    labels: ['Patients', 'Students', 'Supervisors', 'Assistants', 'Appointments', 'Reviews', 'Procedures'],
    datasets: [
      {
        label: 'Counts',
        data: [
          peopleAnalytics.patients,
          peopleAnalytics.students,
          peopleAnalytics.supervisors,
          peopleAnalytics.assistants,
          appointmentsAnalytics.total,
          reviewsAnalytics.total,
          proceduresAnalytics.total,
        ],
        backgroundColor: [
          colorPalette.primary.main,
          colorPalette.secondary.main,
          colorPalette.accent.main,
          colorPalette.primary.main,
          colorPalette.secondary.main,
          colorPalette.accent.main,
          colorPalette.orange.main,
        ],
        borderColor: [
          colorPalette.primary.border,
          colorPalette.secondary.border,
          colorPalette.accent.border,
          colorPalette.primary.border,
          colorPalette.secondary.border,
          colorPalette.accent.border,
          colorPalette.orange.border,
        ],
        borderWidth: 1,
        borderRadius: 6,
        hoverOffset: 4,
      },
    ],
  };

  // Appointments status chart data
  const appointmentsStatusChartData = {
    labels: ['Pending', 'Completed', 'Cancelled'],
    datasets: [
      {
        data: [
          appointmentsAnalytics.pending,
          appointmentsAnalytics.completed,
          appointmentsAnalytics.cancelled,
        ],
        backgroundColor: [
          colorPalette.amber.main,
          colorPalette.accent.main,
          colorPalette.red.main,
        ],
        borderColor: [
          colorPalette.amber.border,
          colorPalette.accent.border,
          colorPalette.red.border,
        ],
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Appointments by month chart data
  const appointmentsByMonthChartData = {
    labels: appointmentsAnalytics.byMonth.map(item => item.month),
    datasets: [
      {
        label: 'Appointments',
        data: appointmentsAnalytics.byMonth.map(item => item.count),
        backgroundColor: colorPalette.primary.main,
        borderColor: colorPalette.primary.border,
        borderWidth: 1,
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // Appointments by day chart data
  const appointmentsByDayChartData = {
    labels: appointmentsAnalytics.byDay.map(item => item.day),
    datasets: [
      {
        label: 'Appointments',
        data: appointmentsAnalytics.byDay.map(item => item.count),
        backgroundColor: colorPalette.secondary.main,
        borderColor: colorPalette.secondary.border,
        borderWidth: 1,
        borderRadius: 6,
      },
    ],
  };

  // Appointments by type chart data
  const appointmentsByTypeChartData = {
    labels: Object.keys(appointmentsAnalytics.byType),
    datasets: [
      {
        data: Object.values(appointmentsAnalytics.byType),
        backgroundColor: Object.keys(appointmentsAnalytics.byType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].main;
        }),
        borderColor: Object.keys(appointmentsAnalytics.byType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].border;
        }),
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Reviews status chart data
  const reviewsStatusChartData = {
    labels: ['Pending', 'Accepted', 'Declined'],
    datasets: [
      {
        data: [
          reviewsAnalytics.pending,
          reviewsAnalytics.accepted,
          reviewsAnalytics.declined,
        ],
        backgroundColor: [
          colorPalette.amber.main,
          colorPalette.accent.main,
          colorPalette.red.main,
        ],
        borderColor: [
          colorPalette.amber.border,
          colorPalette.accent.border,
          colorPalette.red.border,
        ],
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Reviews by procedure type chart data
  const reviewsByProcedureTypeChartData = {
    labels: Object.keys(reviewsAnalytics.byProcedureType),
    datasets: [
      {
        data: Object.values(reviewsAnalytics.byProcedureType),
        backgroundColor: Object.keys(reviewsAnalytics.byProcedureType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].main;
        }),
        borderColor: Object.keys(reviewsAnalytics.byProcedureType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].border;
        }),
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Reviews by month chart data
  const reviewsByMonthChartData = {
    labels: reviewsAnalytics.byMonth.map(item => item.month),
    datasets: [
      {
        label: 'Reviews',
        data: reviewsAnalytics.byMonth.map(item => item.count),
        backgroundColor: colorPalette.secondary.main,
        borderColor: colorPalette.secondary.border,
        borderWidth: 1,
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // Patients by age chart data
  const patientsByAgeChartData = {
    labels: Object.keys(patientsAnalytics.byAge),
    datasets: [
      {
        label: 'Patients',
        data: Object.values(patientsAnalytics.byAge),
        backgroundColor: colorPalette.accent.main,
        borderColor: colorPalette.accent.border,
        borderWidth: 1,
        borderRadius: 6,
      },
    ],
  };

  // Patients by gender chart data
  const patientsByGenderChartData = {
    labels: Object.keys(patientsAnalytics.byGender),
    datasets: [
      {
        data: Object.values(patientsAnalytics.byGender),
        backgroundColor: [
          colorPalette.primary.main,
          colorPalette.secondary.main,
          colorPalette.accent.main,
        ],
        borderColor: [
          colorPalette.primary.border,
          colorPalette.secondary.border,
          colorPalette.accent.border,
        ],
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Patients by chronic disease chart data
  const patientsByChronicDiseaseChartData = {
    labels: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5), // Top 5 chronic diseases
    datasets: [
      {
        label: 'Patients',
        data: Object.values(patientsAnalytics.byChronicDisease).slice(0, 5), // Top 5 chronic diseases
        backgroundColor: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].main;
        }),
        borderColor: Object.keys(patientsAnalytics.byChronicDisease).slice(0, 5).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].border;
        }),
        borderWidth: 1,
        borderRadius: 6,
      },
    ],
  };

  // Procedures by type chart data
  const proceduresByTypeChartData = {
    labels: Object.keys(proceduresAnalytics.byType),
    datasets: [
      {
        data: Object.values(proceduresAnalytics.byType),
        backgroundColor: Object.keys(proceduresAnalytics.byType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].main;
        }),
        borderColor: Object.keys(proceduresAnalytics.byType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].border;
        }),
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Procedures by month chart data
  const proceduresByMonthChartData = {
    labels: proceduresAnalytics.byMonth.map(item => item.month),
    datasets: [
      {
        label: 'Procedures',
        data: proceduresAnalytics.byMonth.map(item => item.count),
        backgroundColor: colorPalette.accent.main,
        borderColor: colorPalette.accent.border,
        borderWidth: 1,
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // Procedures by student chart data
  const proceduresByStudentChartData = {
    labels: Object.keys(proceduresAnalytics.byStudent).slice(0, 10), // Top 10 students
    datasets: [
      {
        label: 'Procedures',
        data: Object.values(proceduresAnalytics.byStudent).slice(0, 10),
        backgroundColor: colorPalette.primary.main,
        borderColor: colorPalette.primary.border,
        borderWidth: 1,
        borderRadius: 6,
      },
    ],
  };

  // Procedures by patient chart data
  const proceduresByPatientChartData = {
    labels: Object.keys(proceduresAnalytics.byPatient).slice(0, 8), // Top 8 patients
    datasets: [
      {
        label: 'Procedures',
        data: Object.values(proceduresAnalytics.byPatient).slice(0, 8),
        backgroundColor: colorPalette.secondary.main,
        borderColor: colorPalette.secondary.border,
        borderWidth: 1,
        borderRadius: 6,
      },
    ],
  };

  // Chart options
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            family: "'Inter', sans-serif",
            size: 12
          },
          padding: 20,
          usePointStyle: true,
          boxWidth: 8
        }
      },
      title: {
        display: false,
        font: {
          family: "'Inter', sans-serif",
          size: 16,
          weight: 'bold'
        },
        color: '#1e3a8a',
        padding: {
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e3a8a',
        bodyColor: '#4b5563',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif"
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold'
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(226, 232, 240, 0.6)'
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
            size: 11
          },
          color: '#64748b'
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
            size: 11
          },
          color: '#64748b'
        }
      }
    }
  };

  // Pie chart options
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          font: {
            family: "'Inter', sans-serif",
            size: 12
          },
          padding: 20,
          usePointStyle: true,
          boxWidth: 8
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e3a8a',
        bodyColor: '#4b5563',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif"
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold'
        }
      }
    },
    cutout: '60%',
    radius: '90%'
  };

  // Line chart options
  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            family: "'Inter', sans-serif",
            size: 12
          },
          padding: 20,
          usePointStyle: true,
          boxWidth: 8
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e3a8a',
        bodyColor: '#4b5563',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif"
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold'
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(226, 232, 240, 0.6)'
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
            size: 11
          },
          color: '#64748b'
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
            size: 11
          },
          color: '#64748b'
        }
      }
    }
  };

  // Helper function to get appointments by month
  function getAppointmentsByMonth(appointments) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const appointmentsByMonth = {};

    // Initialize all months with 0
    months.forEach(month => {
      appointmentsByMonth[month] = 0;
    });

    // Count appointments by month
    appointments.forEach(appointment => {
      const date = new Date(appointment.date);
      const month = months[date.getMonth()];
      appointmentsByMonth[month]++;
    });

    // Convert to array for chart
    return months.map(month => ({
      month,
      count: appointmentsByMonth[month]
    }));
  }

  // Helper function to get appointments by day of week
  function getAppointmentsByDay(appointments) {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const appointmentsByDay = {};

    // Initialize all days with 0
    days.forEach(day => {
      appointmentsByDay[day] = 0;
    });

    // Count appointments by day
    appointments.forEach(appointment => {
      const date = new Date(appointment.date);
      const day = days[date.getDay()];
      appointmentsByDay[day]++;
    });

    // Convert to array for chart
    return days.map(day => ({
      day,
      count: appointmentsByDay[day]
    }));
  }

  // Helper function to get appointments by type
  function getAppointmentsByType(appointments) {
    const appointmentsByType = {};

    // Count appointments by type
    appointments.forEach(appointment => {
      const type = appointment.type || 'Other';
      appointmentsByType[type] = (appointmentsByType[type] || 0) + 1;
    });

    return appointmentsByType;
  }

  // Helper function to get reviews by procedure type
  function getReviewsByProcedureType(reviews) {
    const reviewsByProcedureType = {};

    // Count reviews by procedure type
    reviews.forEach(review => {
      // Skip signature storage reviews
      if (review.patientId && review.patientId.nationalId === 'signature-storage') return;

      let type = 'Other';

      // Handle different possible formats of procedureType
      if (review.procedureType) {
        type = review.procedureType;
      } else if (review.procedure && review.procedure.type) {
        type = review.procedure.type;
      }

      reviewsByProcedureType[type] = (reviewsByProcedureType[type] || 0) + 1;
    });

    // If we have too many categories, limit to top 5 plus "Other"
    const entries = Object.entries(reviewsByProcedureType);
    if (entries.length > 6) {
      const sortedEntries = entries.sort((a, b) => b[1] - a[1]);
      const top5 = sortedEntries.slice(0, 5);
      const others = sortedEntries.slice(5).reduce((sum, entry) => sum + entry[1], 0);

      const result = Object.fromEntries(top5);
      result['Other'] = others;
      return result;
    }

    return reviewsByProcedureType;
  }

  // Helper function to get reviews by month
  function getReviewsByMonth(reviews) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const reviewsByMonth = {};

    // Initialize all months with 0
    months.forEach(month => {
      reviewsByMonth[month] = 0;
    });

    // Count reviews by month
    reviews.forEach(review => {
      try {
        // Skip signature storage reviews
        if (review.patientId && review.patientId.nationalId === 'signature-storage') return;

        // Try different date fields that might exist in the review object
        let dateStr = null;
        if (review.submittedDate) dateStr = review.submittedDate;
        else if (review.createdAt) dateStr = review.createdAt;
        else if (review.date) dateStr = review.date;
        else if (review.reviewedDate) dateStr = review.reviewedDate;

        if (!dateStr) return; // Skip if no valid date found

        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return; // Skip if date is invalid

        const month = months[date.getMonth()];
        reviewsByMonth[month]++;
      } catch (error) {
        console.error('Error processing review date:', error);
      }
    });

    // Convert to array for chart
    return months.map(month => ({
      month,
      count: reviewsByMonth[month]
    }));
  }

  // Helper function to get reviews by student
  function getReviewsByStudent(reviews) {
    const reviewsByStudent = {};

    // Count reviews by student
    reviews.forEach(review => {
      // Skip signature storage reviews
      if (review.patientId && review.patientId.nationalId === 'signature-storage') return;

      // Get student name from different possible formats
      let student = 'Unknown';
      if (review.studentName) {
        student = review.studentName;
      } else if (review.student && review.student.name) {
        student = review.student.name;
      } else if (review.studentId) {
        // Try to find student name from students array
        const foundStudent = students.find(s => s._id === review.studentId || s.studentId === review.studentId);
        if (foundStudent) {
          student = foundStudent.name;
        }
      }

      if (!reviewsByStudent[student]) {
        reviewsByStudent[student] = {
          total: 0,
          accepted: 0,
          declined: 0,
          pending: 0
        };
      }

      reviewsByStudent[student].total++;
      if (review.status === 'accepted') reviewsByStudent[student].accepted++;
      else if (review.status === 'declined' || review.status === 'denied') reviewsByStudent[student].declined++;
      else reviewsByStudent[student].pending++;
    });

    // Sort by total reviews and limit to top 10 students
    const sortedStudents = Object.entries(reviewsByStudent)
      .sort((a, b) => b[1].total - a[1].total)
      .slice(0, 10);

    return Object.fromEntries(sortedStudents);
  }

  // Helper function to calculate average rating
  function calculateAverageRating(reviews, field) {
    // Filter reviews that have a valid rating for the specified field
    const validReviews = reviews.filter(review => {
      // Skip signature storage reviews
      if (review.patientId && review.patientId.nationalId === 'signature-storage') return false;

      // Check direct field
      if (review[field] && typeof review[field] === 'number' && review[field] > 0) {
        return true;
      }

      // Check nested fields (e.g., ratings.procedureQuality)
      if (review.ratings && review.ratings[field] && typeof review.ratings[field] === 'number' && review.ratings[field] > 0) {
        return true;
      }

      return false;
    });

    if (validReviews.length === 0) return 0;

    // Calculate sum considering both direct and nested fields
    const sum = validReviews.reduce((total, review) => {
      if (review[field] && typeof review[field] === 'number') {
        return total + review[field];
      } else if (review.ratings && review.ratings[field] && typeof review.ratings[field] === 'number') {
        return total + review.ratings[field];
      }
      return total;
    }, 0);

    return (sum / validReviews.length).toFixed(1);
  }

  // Helper function to get patients age distribution
  function getPatientsAgeDistribution(patients) {
    const ageGroups = {
      '0-18': 0,
      '19-30': 0,
      '31-45': 0,
      '46-60': 0,
      '61+': 0
    };

    patients.forEach(patient => {
      const age = patient.age || 0;
      if (age <= 18) ageGroups['0-18']++;
      else if (age <= 30) ageGroups['19-30']++;
      else if (age <= 45) ageGroups['31-45']++;
      else if (age <= 60) ageGroups['46-60']++;
      else ageGroups['61+']++;
    });

    return ageGroups;
  }

  // Helper function to get patients gender distribution
  function getPatientsGenderDistribution(patients) {
    const genderDistribution = {
      'Male': 0,
      'Female': 0,
      'Other': 0
    };

    patients.forEach(patient => {
      const gender = patient.gender || 'Other';
      genderDistribution[gender] = (genderDistribution[gender] || 0) + 1;
    });

    return genderDistribution;
  }

  // Helper function to get patients chronic disease distribution
  function getPatientsChronicDiseaseDistribution(patients) {
    const diseaseDistribution = {};

    patients.forEach(patient => {
      if (patient.medicalInfo && patient.medicalInfo.chronicDiseases) {
        patient.medicalInfo.chronicDiseases.forEach(disease => {
          diseaseDistribution[disease] = (diseaseDistribution[disease] || 0) + 1;
        });
      }
    });

    // Sort by count and return
    return Object.fromEntries(
      Object.entries(diseaseDistribution).sort((a, b) => b[1] - a[1])
    );
  }

  // Helper function to get procedures by type
  function getProceduresByType(sheets) {
    const proceduresByType = {};

    sheets.forEach(sheet => {
      const type = sheet.type || 'Other';
      proceduresByType[type] = (proceduresByType[type] || 0) + 1;
    });

    return proceduresByType;
  }

  // Helper function to get procedures by month
  function getProceduresByMonth(sheets) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const proceduresByMonth = {};

    // Initialize all months with 0
    months.forEach(month => {
      proceduresByMonth[month] = 0;
    });

    // Count procedures by month
    sheets.forEach(sheet => {
      try {
        const date = new Date(sheet.createdAt);
        if (isNaN(date.getTime())) return; // Skip if date is invalid

        const month = months[date.getMonth()];
        proceduresByMonth[month]++;
      } catch (error) {
        console.error('Error processing sheet date:', error);
      }
    });

    // Convert to array for chart
    return months.map(month => ({
      month,
      count: proceduresByMonth[month]
    }));
  }

  // Helper function to get procedures by student
  function getProceduresByStudent(sheets) {
    const proceduresByStudent = {};

    sheets.forEach(sheet => {
      const student = sheet.studentName || 'Unknown Student';
      proceduresByStudent[student] = (proceduresByStudent[student] || 0) + 1;
    });

    // Sort by count and return
    return Object.fromEntries(
      Object.entries(proceduresByStudent).sort((a, b) => b[1] - a[1])
    );
  }

  // Helper function to get procedures by patient
  function getProceduresByPatient(sheets) {
    const proceduresByPatient = {};

    sheets.forEach(sheet => {
      const patient = sheet.patientName || 'Unknown Patient';
      proceduresByPatient[patient] = (proceduresByPatient[patient] || 0) + 1;
    });

    // Sort by count and return top 10
    return Object.fromEntries(
      Object.entries(proceduresByPatient).sort((a, b) => b[1] - a[1]).slice(0, 10)
    );
  }

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-8">
                <h1 className={`text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Analytics</h1>
                <p className={`text-[${websiteColorPalette.text}]`}>View analytics for your university</p>
              </div>

              {/* Analytics Overview Cards */}
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
              >
                <motion.div
                  variants={item}
                  className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                >
                  <div className="flex items-center">
                    <div className="bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                      <FaCalendarAlt className={`h-6 w-6 text-[${websiteColorPalette.primary}]`} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Total Appointments</p>
                      <p className="text-2xl font-bold text-blue-900">{appointmentsAnalytics.total}</p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  variants={item}
                  className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                >
                  <div className="flex items-center">
                    <div className="bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                      <FaStar className={`h-6 w-6 text-[${websiteColorPalette.primary}]`} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Total Reviews</p>
                      <p className="text-2xl font-bold text-blue-900">{reviewsAnalytics.total}</p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  variants={item}
                  className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                >
                  <div className="flex items-center">
                    <div className="bg-blue-50 w-14 h-14 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                      <FaFileMedical className={`h-6 w-6 text-[${websiteColorPalette.primary}]`} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Total Procedures</p>
                      <p className="text-2xl font-bold text-blue-900">{proceduresAnalytics.total}</p>
                    </div>
                  </div>
                </motion.div>
              </motion.div>

              {/* Tabs Navigation */}
              <motion.div
                variants={item}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden mb-8"
              >
                <div className="border-b border-gray-200">
                  <nav className="flex -mb-px">
                    <button
                      className={`py-4 px-6 font-medium text-sm border-b-2 ${
                        activeTab === 'overview'
                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('overview')}
                    >
                      Overview
                    </button>
                    <button
                      className={`py-4 px-6 font-medium text-sm border-b-2 ${
                        activeTab === 'appointments'
                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('appointments')}
                    >
                      Appointments
                    </button>
                    <button
                      className={`py-4 px-6 font-medium text-sm border-b-2 ${
                        activeTab === 'reviews'
                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('reviews')}
                    >
                      Reviews
                    </button>
                    <button
                      className={`py-4 px-6 font-medium text-sm border-b-2 ${
                        activeTab === 'procedures'
                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('procedures')}
                    >
                      Procedures
                    </button>
                    <button
                      className={`py-4 px-6 font-medium text-sm border-b-2 ${
                        activeTab === 'patients'
                          ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('patients')}
                    >
                      Patients
                    </button>
                  </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {/* Overview Tab */}
                  {activeTab === 'overview' && (
                    <div>
                      <div className="flex justify-between items-center mb-6">
                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                          <FaChartLine className="h-5 w-5 mr-2" />
                          University Overview
                        </h2>
                      </div>
                      <div className="bg-blue-50 p-6 rounded-lg">
                        <div className="h-96">
                          <Bar data={overviewChartData} options={barChartOptions} />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Appointments Tab */}
                  {activeTab === 'appointments' && (
                    <div>
                      <div className="flex justify-between items-center mb-6">
                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                          <FaCalendarAlt className="h-5 w-5 mr-2" />
                          Appointment Analytics
                        </h2>
                      </div>

                      {/* Appointment Stats */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Total</p>
                          <p className="text-2xl font-bold text-blue-900">{appointmentsAnalytics.total}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Pending</p>
                          <p className="text-2xl font-bold text-blue-900">{appointmentsAnalytics.pending}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Completed</p>
                          <p className="text-2xl font-bold text-blue-900">{appointmentsAnalytics.completed}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Cancelled</p>
                          <p className="text-2xl font-bold text-blue-900">{appointmentsAnalytics.cancelled}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Today</p>
                          <p className="text-2xl font-bold text-blue-900">{appointmentsAnalytics.today}</p>
                        </div>
                      </div>

                      {/* Appointment Charts */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Appointment Status
                          </h3>
                          <div className="h-64">
                            <Doughnut data={appointmentsStatusChartData} options={pieChartOptions} />
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Appointment Types
                          </h3>
                          <div className="h-64">
                            <Pie data={appointmentsByTypeChartData} options={pieChartOptions} />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartArea className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Appointments by Month
                          </h3>
                          <div className="h-64">
                            <Line data={appointmentsByMonthChartData} options={lineChartOptions} />
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Appointments by Day
                          </h3>
                          <div className="h-64">
                            <Bar data={appointmentsByDayChartData} options={barChartOptions} />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Reviews Tab */}
                  {activeTab === 'reviews' && (
                    <div>
                      <div className="flex justify-between items-center mb-6">
                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                          <FaStar className="h-5 w-5 mr-2" />
                          Review Analytics
                        </h2>
                      </div>

                      {/* Review Stats */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Total</p>
                          <p className="text-2xl font-bold text-blue-900">{reviewsAnalytics.total}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Pending</p>
                          <p className="text-2xl font-bold text-blue-900">{reviewsAnalytics.pending}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Accepted</p>
                          <p className="text-2xl font-bold text-blue-900">{reviewsAnalytics.accepted}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Declined</p>
                          <p className="text-2xl font-bold text-blue-900">{reviewsAnalytics.declined}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Avg. Quality</p>
                          <p className="text-2xl font-bold text-blue-900">{reviewsAnalytics.avgProcedureQuality}/5</p>
                        </div>
                      </div>

                      {/* Review Charts */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Review Status
                          </h3>
                          <div className="h-64">
                            <Doughnut data={reviewsStatusChartData} options={pieChartOptions} />
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Procedure Types
                          </h3>
                          <div className="h-64">
                            <Pie data={reviewsByProcedureTypeChartData} options={pieChartOptions} />
                          </div>
                        </div>
                      </div>

                      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-6">
                        <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                          <FaChartArea className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                          Reviews by Month
                        </h3>
                        <div className="h-64">
                          <Line data={reviewsByMonthChartData} options={lineChartOptions} />
                        </div>
                      </div>

                      {/* Top Students Table */}
                      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                          <FaUserGraduate className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                          Student Performance
                        </h3>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Student
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Total Reviews
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Accepted
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Declined
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Pending
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {Object.entries(reviewsAnalytics.byStudent)
                                .sort((a, b) => b[1].total - a[1].total)
                                .slice(0, 5)
                                .map(([student, stats]) => (
                                  <tr key={student}>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                      {student}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                      {stats.total}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        {stats.accepted}
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        {stats.declined}
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        {stats.pending}
                                      </span>
                                    </td>
                                  </tr>
                                ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Procedures Tab */}
                  {activeTab === 'procedures' && (
                    <div>
                      <div className="flex justify-between items-center mb-6">
                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                          <FaChartPie className="h-5 w-5 mr-2" />
                          Procedures Analytics
                        </h2>
                      </div>

                      {/* Procedures Stats */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Total</p>
                          <p className="text-2xl font-bold text-blue-900">{proceduresAnalytics.total}</p>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <p className="text-sm font-medium text-gray-500">Recent Sheets</p>
                          <p className="text-2xl font-bold text-blue-900">{proceduresAnalytics.recentSheets.length}</p>
                        </div>
                      </div>

                      {/* Procedures Charts */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Procedures by Type
                          </h3>
                          <div className="h-64">
                            <Pie data={proceduresByTypeChartData} options={pieChartOptions} />
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartArea className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Procedures by Month
                          </h3>
                          <div className="h-64">
                            <Line data={proceduresByMonthChartData} options={lineChartOptions} />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Top Students by Procedures
                          </h3>
                          <div className="h-64">
                            <Bar data={proceduresByStudentChartData} options={barChartOptions} />
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Top Patients by Procedures
                          </h3>
                          <div className="h-64">
                            <Bar data={proceduresByPatientChartData} options={barChartOptions} />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaFileMedical className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Recent Procedures
                          </h3>
                          <div className="h-64 overflow-y-auto">
                            <div className="space-y-2">
                              {proceduresAnalytics.recentSheets.map((sheet, index) => (
                                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <p className="text-sm font-medium text-gray-900">{sheet.type}</p>
                                      <p className="text-xs text-gray-500">Patient: {sheet.patientName}</p>
                                      <p className="text-xs text-gray-500">Student: {sheet.studentName}</p>
                                    </div>
                                    <span className="text-xs text-gray-400">
                                      {new Date(sheet.createdAt).toLocaleDateString()}
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Procedures Summary
                          </h3>
                          <div className="h-64 flex flex-col justify-center">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="text-center">
                                <p className="text-2xl font-bold text-blue-900">{proceduresAnalytics.total}</p>
                                <p className="text-sm text-gray-500">Total Procedures</p>
                              </div>
                              <div className="text-center">
                                <p className="text-2xl font-bold text-green-900">{Object.keys(proceduresAnalytics.byType).length}</p>
                                <p className="text-sm text-gray-500">Procedure Types</p>
                              </div>
                              <div className="text-center">
                                <p className="text-2xl font-bold text-purple-900">{Object.keys(proceduresAnalytics.byStudent).length}</p>
                                <p className="text-sm text-gray-500">Active Students</p>
                              </div>
                              <div className="text-center">
                                <p className="text-2xl font-bold text-orange-900">{Object.keys(proceduresAnalytics.byPatient).length}</p>
                                <p className="text-sm text-gray-500">Patients Treated</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Patients Tab */}
                  {activeTab === 'patients' && (
                    <div>
                      <div className="flex justify-between items-center mb-6">
                        <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                          <FaUserAlt className="h-5 w-5 mr-2" />
                          Patient Analytics
                        </h2>
                      </div>

                      {/* Patient Charts */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartPie className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Gender Distribution
                          </h3>
                          <div className="h-64">
                            <Pie data={patientsByGenderChartData} options={pieChartOptions} />
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                          <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                            <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Age Distribution
                          </h3>
                          <div className="h-64">
                            <Bar data={patientsByAgeChartData} options={barChartOptions} />
                          </div>
                        </div>
                      </div>

                      {/* Chronic Diseases */}
                      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <h3 className="text-lg font-medium text-gray-700 mb-3 flex items-center">
                          <FaChartBar className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                          Top Chronic Diseases
                        </h3>
                        <div className="h-64">
                          <Bar data={patientsByChronicDiseaseChartData} options={barChartOptions} />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Analytics;