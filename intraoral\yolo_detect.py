import sys
import json
import os
from ultralytics import YOLO
import cv2
import numpy as np

# Suppress YOLOv8 progress output
os.environ['ULTRALYTICS_VERBOSE'] = 'False'

if len(sys.argv) < 3:
    print(json.dumps({"error": "Usage: python yolo_detect.py <model_path> <image_path>"}))
    sys.exit(1)

model_path = sys.argv[1]
image_path = sys.argv[2]

try:
    # Suppress stdout temporarily to capture YOLOv8 output
    import io
    import contextlib
    
    # Capture YOLOv8 output
    f = io.StringIO()
    with contextlib.redirect_stdout(f):
        model = YOLO(model_path)
        results = model(image_path, verbose=False)  # Disable verbose output
    
    detections = []
    names = model.names
    
    # Load the original image for annotation
    original_image = cv2.imread(image_path)
    annotated_image = original_image.copy()
    
    for r in results:
        boxes = r.boxes
        if boxes is not None and hasattr(boxes, 'xyxy'):
            for i in range(len(boxes)):
                box = boxes.xyxy[i].tolist()
                conf = boxes.conf[i].item()
                cls = int(boxes.cls[i].item())
                class_name = names[cls] if cls in names else str(cls)
                x1, y1, x2, y2 = box
                img_w, img_h = r.orig_shape[1], r.orig_shape[0]
                
                # Convert normalized coordinates to pixel coordinates
                x1_px, y1_px = int(x1), int(y1)
                x2_px, y2_px = int(x2), int(y2)
                
                # Draw bounding box
                color = (0, 255, 0) if class_name == 'healthy tooth' else (0, 0, 255) if class_name == 'decaycavity' else (0, 255, 255)
                cv2.rectangle(annotated_image, (x1_px, y1_px), (x2_px, y2_px), color, 2)
                
                # Draw label
                label = f"{class_name} {conf:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                cv2.rectangle(annotated_image, (x1_px, y1_px - label_size[1] - 10), (x1_px + label_size[0], y1_px), color, -1)
                cv2.putText(annotated_image, label, (x1_px, y1_px - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                
                detections.append({
                    "class": class_name,
                    "confidence": conf,
                    "bbox": {
                        "x": x1 / img_w,
                        "y": y1 / img_h,
                        "width": (x2 - x1) / img_w,
                        "height": (y2 - y1) / img_h
                    }
                })
    
    # Save annotated image
    annotated_path = image_path.replace('.jpg', '_annotated.jpg').replace('.png', '_annotated.png')
    cv2.imwrite(annotated_path, annotated_image)
    
    # Print only the JSON result
    print(json.dumps({
        "success": True, 
        "results": detections,
        "annotated_image_path": annotated_path
    }))
    
except Exception as e:
    print(json.dumps({"error": str(e)}))
    sys.exit(1) 