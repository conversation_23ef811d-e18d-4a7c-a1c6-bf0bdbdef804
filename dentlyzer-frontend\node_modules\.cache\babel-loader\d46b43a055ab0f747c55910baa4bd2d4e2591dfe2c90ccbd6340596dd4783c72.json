{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\router.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$();\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { useAuth } from './context/AuthContext';\nimport './components/i18n';\n//main imports\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport UniversityServices from './pages/UniversityServices';\nimport Contact from './pages/Contact';\nimport Login from './pages/Login';\nimport TryAI from './pages/TryAI';\nimport Universities from './pages/Universities';\nimport UniversityInfo from './pages/UniversityInfo';\nimport UniversityBook from './pages/UniversityBook';\nimport UniversityConfirmation from './pages/UniversityConfirmation';\nimport Profile from './components/Profile';\nimport ForgotPassword from './components/ForgotPassword';\nimport Support from './components/Support';\n//student\nimport StudentDashboard from './student/Dashboard';\nimport StudentCalendar from './student/Calendar';\nimport StudentPatients from './student/Patients';\nimport StudentAnalytics from './student/Analytics';\nimport StudentReviews from './student/Reviews';\nimport StudentPatientProfile from './student/PatientProfile';\nimport StudentGallery from './student/Gallery';\n// import StudentXRay from './student/XRay'; // No longer needed after merging with Gallery\nimport StudentToothChart from './student/ToothChart';\nimport StudentAppointments from './student/Appointments';\nimport StudentReviewSteps from './student/ReviewSteps';\nimport StudentSheets from './student/Sheets';\nimport StudentHistory from './student/History';\nimport StudentConsent from './student/Consent';\nimport StudentLab from './student/Lab';\n//supervisor\nimport SupervisorDashboard from './supervisor/Dashboard';\n//admin\nimport AdminDashboard from './admin/Dashboard';\nimport AdminPeople from './admin/People';\nimport AdminAppointments from './admin/Appointments';\nimport AdminAnalytics from './admin/Analytics';\nimport AdminNews from './admin/News';\nimport AdminReviews from './admin/Reviews';\n//superadmin\nimport SuperadminDashboard from './superadmin/Dashboard';\nimport SuperadminUniversities from './superadmin/Universities';\nimport SuperadminAnalytics from './superadmin/Analytics';\nimport SuperadminNews from './superadmin/News';\nimport SuperadminAccounts from './superadmin/Accounts';\nimport SuperadminActivity from './superadmin/Activity';\n//dentist\n// Dentist routes not currently used in the application\n// import DentistDashboard from './dentist/Dashboard';\n// import DentistCalendar from './dentist/Calendar';\n// import DentistPatients from './dentist/Patients';\n// import DentistMessages from './dentist/Messages';\n// import DentistAnalytics from './dentist/Analytics';\n// import DentistPatientProfile from './dentist/PatientProfile';\n// import DentistGallery from './dentist/Gallery';\n// import DentistXRay from './dentist/XRay';\n// import DentistToothChart from './dentist/ToothChart';\n// import DentistAppointments from './dentist/Appointments';\n//assistant\nimport AssistantDashboard from './assistant/Dashboard';\nimport AssistantAppointments from './assistant/Appointments';\nimport AssistantAnalytics from './assistant/Analytics';\nimport AssistantProcedureRequests from './assistant/ProcedureRequests';\nimport AssistantPatients from './assistant/Patients';\n\n// Generic ProtectedRoute component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  if (!user) return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 21\n  }, this);\n  return children;\n};\n\n// Role-specific wrappers\n_s(ProtectedRoute, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nconst StudentRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  children: /*#__PURE__*/_jsxDEV(StudentCheck, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 56\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 78,\n  columnNumber: 40\n}, this);\n_c2 = StudentRoute;\nconst SupervisorRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  children: /*#__PURE__*/_jsxDEV(SupervisorCheck, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 59\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 79,\n  columnNumber: 43\n}, this);\n_c3 = SupervisorRoute;\nconst AdminRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  children: /*#__PURE__*/_jsxDEV(AdminCheck, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 54\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 80,\n  columnNumber: 38\n}, this);\n_c4 = AdminRoute;\nconst SuperadminRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  children: /*#__PURE__*/_jsxDEV(SuperadminCheck, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 59\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 81,\n  columnNumber: 43\n}, this);\n_c5 = SuperadminRoute;\nconst AssistantRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  children: /*#__PURE__*/_jsxDEV(AssistantCheck, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 58\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 82,\n  columnNumber: 42\n}, this);\n\n// Role-specific checks\n_c6 = AssistantRoute;\nconst StudentCheck = ({\n  children\n}) => {\n  _s2();\n  const {\n    user\n  } = useAuth();\n  return user.role === 'student' ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 47\n  }, this);\n};\n_s2(StudentCheck, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c7 = StudentCheck;\nconst SupervisorCheck = ({\n  children\n}) => {\n  _s3();\n  const {\n    user\n  } = useAuth();\n  return user.role === 'supervisor' ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 50\n  }, this);\n};\n_s3(SupervisorCheck, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c8 = SupervisorCheck;\nconst AdminCheck = ({\n  children\n}) => {\n  _s4();\n  const {\n    user\n  } = useAuth();\n  return user.role === 'admin' ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 45\n  }, this);\n};\n_s4(AdminCheck, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c9 = AdminCheck;\nconst SuperadminCheck = ({\n  children\n}) => {\n  _s5();\n  const {\n    user\n  } = useAuth();\n  return user.role === 'superadmin' ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 50\n  }, this);\n};\n_s5(SuperadminCheck, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c10 = SuperadminCheck;\nconst AssistantCheck = ({\n  children\n}) => {\n  _s6();\n  const {\n    user\n  } = useAuth();\n  return user.role === 'assistant' ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 49\n  }, this);\n};\n\n// Dynamic dashboard redirect based on role\n_s6(AssistantCheck, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c11 = AssistantCheck;\nconst DashboardRedirect = () => {\n  _s7();\n  const {\n    user\n  } = useAuth();\n  if (!user) return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 21\n  }, this);\n  switch (user.role) {\n    case 'student':\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/student/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 14\n      }, this);\n    case 'supervisor':\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/supervisor/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 14\n      }, this);\n    case 'admin':\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/admin/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 14\n      }, this);\n    case 'superadmin':\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/superadmin/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 14\n      }, this);\n    case 'assistant':\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/assistant/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 14\n      }, this);\n    default:\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 14\n      }, this);\n    // Fallback for unexpected roles\n  }\n};\n_s7(DashboardRedirect, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c12 = DashboardRedirect;\nconst AppRoutes = () => /*#__PURE__*/_jsxDEV(Routes, {\n  children: [/*#__PURE__*/_jsxDEV(Route, {\n    path: \"/\",\n    element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 30\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/about\",\n    element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 37\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/universityServices\",\n    element: /*#__PURE__*/_jsxDEV(UniversityServices, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 50\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/contact\",\n    element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 39\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/login\",\n    element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 37\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/try-ai\",\n    element: /*#__PURE__*/_jsxDEV(TryAI, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 38\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/universities\",\n    element: /*#__PURE__*/_jsxDEV(Universities, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 44\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/university-info/:universityId\",\n    element: /*#__PURE__*/_jsxDEV(UniversityInfo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 61\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/university-book\",\n    element: /*#__PURE__*/_jsxDEV(UniversityBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 47\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/university-confirmation\",\n    element: /*#__PURE__*/_jsxDEV(UniversityConfirmation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 55\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/forgot-password\",\n    element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 47\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(DashboardRedirect, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 39\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/student/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 61\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 47\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/calendar\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentCalendar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 52\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 38\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patients\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentPatients, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 52\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 38\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/analytics\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentAnalytics, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 53\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 39\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/reviews\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentReviews, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 51\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 37\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/lab\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentLab, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 47\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 33\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentPatientProfile, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 70\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 56\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/gallery\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentGallery, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 78\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 64\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/xrays\",\n    element: /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"../gallery\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 62\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/toothchart\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentToothChart, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 81\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 67\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/appointments\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentAppointments, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 83\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 69\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/reviewsteps\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentReviewSteps, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 82\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 68\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/sheets\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentSheets, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 77\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 63\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/history\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentHistory, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 78\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 64\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/patientprofile/:nationalId/consent\",\n    element: /*#__PURE__*/_jsxDEV(StudentRoute, {\n      children: /*#__PURE__*/_jsxDEV(StudentConsent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 78\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 64\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/profile\",\n    element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 37\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/support\",\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      children: /*#__PURE__*/_jsxDEV(Support, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 53\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 37\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/supervisor/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(SupervisorRoute, {\n      children: /*#__PURE__*/_jsxDEV(SupervisorDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 67\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 50\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/admin/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(AdminRoute, {\n      children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 57\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 45\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/admin/people\",\n    element: /*#__PURE__*/_jsxDEV(AdminRoute, {\n      children: /*#__PURE__*/_jsxDEV(AdminPeople, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 54\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 42\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/admin/appointments\",\n    element: /*#__PURE__*/_jsxDEV(AdminRoute, {\n      children: /*#__PURE__*/_jsxDEV(AdminAppointments, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 60\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 48\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/admin/analytics\",\n    element: /*#__PURE__*/_jsxDEV(AdminRoute, {\n      children: /*#__PURE__*/_jsxDEV(AdminAnalytics, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 57\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 45\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/admin/news\",\n    element: /*#__PURE__*/_jsxDEV(AdminRoute, {\n      children: /*#__PURE__*/_jsxDEV(AdminNews, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 52\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 40\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/admin/reviews\",\n    element: /*#__PURE__*/_jsxDEV(AdminRoute, {\n      children: /*#__PURE__*/_jsxDEV(AdminReviews, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 55\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 43\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/superadmin/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(SuperadminRoute, {\n      children: /*#__PURE__*/_jsxDEV(SuperadminDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 67\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 50\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/superadmin/universities\",\n    element: /*#__PURE__*/_jsxDEV(SuperadminRoute, {\n      children: /*#__PURE__*/_jsxDEV(SuperadminUniversities, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 70\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 53\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/superadmin/analytics\",\n    element: /*#__PURE__*/_jsxDEV(SuperadminRoute, {\n      children: /*#__PURE__*/_jsxDEV(SuperadminAnalytics, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 67\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 50\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/superadmin/activity\",\n    element: /*#__PURE__*/_jsxDEV(SuperadminRoute, {\n      children: /*#__PURE__*/_jsxDEV(SuperadminActivity, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 66\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 49\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/superadmin/news\",\n    element: /*#__PURE__*/_jsxDEV(SuperadminRoute, {\n      children: /*#__PURE__*/_jsxDEV(SuperadminNews, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 62\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 45\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/superadmin/accounts\",\n    element: /*#__PURE__*/_jsxDEV(SuperadminRoute, {\n      children: /*#__PURE__*/_jsxDEV(SuperadminAccounts, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 66\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 49\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/assistant/dashboard\",\n    element: /*#__PURE__*/_jsxDEV(AssistantRoute, {\n      children: /*#__PURE__*/_jsxDEV(AssistantDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 65\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 49\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/assistant/appointments\",\n    element: /*#__PURE__*/_jsxDEV(AssistantRoute, {\n      children: /*#__PURE__*/_jsxDEV(AssistantAppointments, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 68\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 52\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/assistant/procedure-requests\",\n    element: /*#__PURE__*/_jsxDEV(AssistantRoute, {\n      children: /*#__PURE__*/_jsxDEV(AssistantProcedureRequests, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 74\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 58\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/assistant/analytics\",\n    element: /*#__PURE__*/_jsxDEV(AssistantRoute, {\n      children: /*#__PURE__*/_jsxDEV(AssistantAnalytics, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 65\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 49\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"/assistant/patients\",\n    element: /*#__PURE__*/_jsxDEV(AssistantRoute, {\n      children: /*#__PURE__*/_jsxDEV(AssistantPatients, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 64\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 48\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Route, {\n    path: \"*\",\n    element: /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 30\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 198,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 129,\n  columnNumber: 3\n}, this);\n_c13 = AppRoutes;\nexport default AppRoutes;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"StudentRoute\");\n$RefreshReg$(_c3, \"SupervisorRoute\");\n$RefreshReg$(_c4, \"AdminRoute\");\n$RefreshReg$(_c5, \"SuperadminRoute\");\n$RefreshReg$(_c6, \"AssistantRoute\");\n$RefreshReg$(_c7, \"StudentCheck\");\n$RefreshReg$(_c8, \"SupervisorCheck\");\n$RefreshReg$(_c9, \"AdminCheck\");\n$RefreshReg$(_c10, \"SuperadminCheck\");\n$RefreshReg$(_c11, \"AssistantCheck\");\n$RefreshReg$(_c12, \"DashboardRedirect\");\n$RefreshReg$(_c13, \"AppRoutes\");", "map": {"version": 3, "names": ["Routes", "Route", "Navigate", "useAuth", "Home", "About", "UniversityServices", "Contact", "<PERSON><PERSON>", "TryAI", "Universities", "UniversityInfo", "UniversityBook", "UniversityConfirmation", "Profile", "ForgotPassword", "Support", "StudentDashboard", "StudentCalendar", "StudentPatients", "StudentAnalytics", "StudentReviews", "StudentPatientProfile", "StudentGallery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StudentAppointments", "StudentReviewSteps", "StudentSheets", "StudentHistory", "StudentConsent", "StudentLab", "SupervisorDashboard", "AdminDashboard", "AdminPeople", "AdminAppointments", "AdminAnalytics", "AdminNews", "AdminReviews", "SuperadminDashboard", "SuperadminUniversities", "SuperadminAnalytics", "SuperadminNews", "SuperadminAccounts", "SuperadminActivity", "AssistantDashboard", "AssistantAppointments", "AssistantAnalytics", "AssistantProcedureRequests", "AssistantPatients", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "StudentRoute", "StudentCheck", "_c2", "SupervisorRoute", "SupervisorCheck", "_c3", "AdminRoute", "Admin<PERSON>heck", "_c4", "SuperadminRoute", "SuperadminCheck", "_c5", "<PERSON><PERSON><PERSON><PERSON>", "Assistant<PERSON><PERSON><PERSON>", "_c6", "_s2", "role", "_c7", "_s3", "_c8", "_s4", "_c9", "_s5", "_c10", "_s6", "_c11", "DashboardRedirect", "_s7", "_c12", "AppRoutes", "path", "element", "_c13", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/router.js"], "sourcesContent": ["import { Routes, Route, Navigate } from 'react-router-dom';\r\nimport { useAuth } from './context/AuthContext';\r\nimport './components/i18n';\r\n//main imports\r\nimport Home from './pages/Home';\r\nimport About  from './pages/About';\r\nimport UniversityServices from './pages/UniversityServices';\r\nimport Contact from './pages/Contact';\r\nimport Login from './pages/Login';\r\nimport TryAI from './pages/TryAI'\r\nimport Universities from './pages/Universities';\r\nimport UniversityInfo from './pages/UniversityInfo';\r\nimport UniversityBook from './pages/UniversityBook';\r\nimport UniversityConfirmation from './pages/UniversityConfirmation';\r\nimport Profile from './components/Profile'\r\nimport ForgotPassword from './components/ForgotPassword';\r\nimport Support from './components/Support';\r\n//student\r\nimport StudentDashboard from './student/Dashboard';\r\nimport StudentCalendar from './student/Calendar';\r\nimport StudentPatients from './student/Patients';\r\nimport StudentAnalytics from './student/Analytics';\r\nimport StudentReviews from './student/Reviews';\r\nimport StudentPatientProfile from './student/PatientProfile';\r\nimport StudentGallery from './student/Gallery';\r\n// import StudentXRay from './student/XRay'; // No longer needed after merging with Gallery\r\nimport StudentToothChart from './student/ToothChart';\r\nimport StudentAppointments from './student/Appointments';\r\nimport StudentReviewSteps from './student/ReviewSteps'\r\nimport StudentSheets from './student/Sheets'\r\nimport StudentHistory from './student/History'\r\nimport StudentConsent from './student/Consent'\r\nimport StudentLab from './student/Lab'\r\n//supervisor\r\nimport SupervisorDashboard from './supervisor/Dashboard';\r\n//admin\r\nimport AdminDashboard from './admin/Dashboard';\r\nimport AdminPeople from './admin/People';\r\nimport AdminAppointments from './admin/Appointments';\r\nimport AdminAnalytics from './admin/Analytics';\r\nimport AdminNews from './admin/News';\r\nimport AdminReviews from './admin/Reviews';\r\n//superadmin\r\nimport SuperadminDashboard from './superadmin/Dashboard';\r\nimport SuperadminUniversities from './superadmin/Universities';\r\nimport SuperadminAnalytics from './superadmin/Analytics';\r\nimport SuperadminNews from './superadmin/News';\r\nimport SuperadminAccounts from './superadmin/Accounts';\r\nimport SuperadminActivity from './superadmin/Activity';\r\n//dentist\r\n// Dentist routes not currently used in the application\r\n// import DentistDashboard from './dentist/Dashboard';\r\n// import DentistCalendar from './dentist/Calendar';\r\n// import DentistPatients from './dentist/Patients';\r\n// import DentistMessages from './dentist/Messages';\r\n// import DentistAnalytics from './dentist/Analytics';\r\n// import DentistPatientProfile from './dentist/PatientProfile';\r\n// import DentistGallery from './dentist/Gallery';\r\n// import DentistXRay from './dentist/XRay';\r\n// import DentistToothChart from './dentist/ToothChart';\r\n// import DentistAppointments from './dentist/Appointments';\r\n//assistant\r\nimport AssistantDashboard from './assistant/Dashboard';\r\nimport AssistantAppointments from './assistant/Appointments';\r\nimport AssistantAnalytics from './assistant/Analytics';\r\nimport AssistantProcedureRequests from './assistant/ProcedureRequests';\r\nimport AssistantPatients from './assistant/Patients';\r\n\r\n\r\n// Generic ProtectedRoute component\r\nconst ProtectedRoute = ({ children }) => {\r\n  const { user } = useAuth();\r\n  if (!user) return <Navigate to=\"/login\" replace />;\r\n  return children;\r\n};\r\n\r\n// Role-specific wrappers\r\nconst StudentRoute = ({ children }) => <ProtectedRoute><StudentCheck>{children}</StudentCheck></ProtectedRoute>;\r\nconst SupervisorRoute = ({ children }) => <ProtectedRoute><SupervisorCheck>{children}</SupervisorCheck></ProtectedRoute>;\r\nconst AdminRoute = ({ children }) => <ProtectedRoute><AdminCheck>{children}</AdminCheck></ProtectedRoute>;\r\nconst SuperadminRoute = ({ children }) => <ProtectedRoute><SuperadminCheck>{children}</SuperadminCheck></ProtectedRoute>;\r\nconst AssistantRoute = ({ children }) => <ProtectedRoute><AssistantCheck>{children}</AssistantCheck></ProtectedRoute>;\r\n\r\n// Role-specific checks\r\nconst StudentCheck = ({ children }) => {\r\n  const { user } = useAuth();\r\n  return user.role === 'student' ? children : <Navigate to=\"/dashboard\" replace />;\r\n};\r\nconst SupervisorCheck = ({ children }) => {\r\n  const { user } = useAuth();\r\n  return user.role === 'supervisor' ? children : <Navigate to=\"/dashboard\" replace />;\r\n};\r\nconst AdminCheck = ({ children }) => {\r\n  const { user } = useAuth();\r\n  return user.role === 'admin' ? children : <Navigate to=\"/dashboard\" replace />;\r\n};\r\nconst SuperadminCheck = ({ children }) => {\r\n  const { user } = useAuth();\r\n  return user.role === 'superadmin' ? children : <Navigate to=\"/dashboard\" replace />;\r\n};\r\nconst AssistantCheck = ({ children }) => {\r\n  const { user } = useAuth();\r\n  return user.role === 'assistant' ? children : <Navigate to=\"/dashboard\" replace />;\r\n};\r\n\r\n// Dynamic dashboard redirect based on role\r\nconst DashboardRedirect = () => {\r\n  const { user } = useAuth();\r\n  if (!user) return <Navigate to=\"/login\" replace />;\r\n\r\n  switch (user.role) {\r\n    case 'student':\r\n      return <Navigate to=\"/student/dashboard\" replace />;\r\n    case 'supervisor':\r\n      return <Navigate to=\"/supervisor/dashboard\" replace />;\r\n    case 'admin':\r\n      return <Navigate to=\"/admin/dashboard\" replace />;\r\n    case 'superadmin':\r\n      return <Navigate to=\"/superadmin/dashboard\" replace />;\r\n    case 'assistant':\r\n      return <Navigate to=\"/assistant/dashboard\" replace />;\r\n\r\n    default:\r\n      return <Navigate to=\"/\" replace />; // Fallback for unexpected roles\r\n  }\r\n};\r\n\r\nconst AppRoutes = () => (\r\n  <Routes>\r\n    {/* Public Routes */}\r\n    <Route path=\"/\" element={<Home />} />\r\n      <Route path=\"/about\" element={<About />} />\r\n      <Route path=\"/universityServices\" element={<UniversityServices />} />\r\n      <Route path=\"/contact\" element={<Contact />} />\r\n      <Route path=\"/login\" element={<Login />} />\r\n      <Route path=\"/try-ai\" element={<TryAI />} />\r\n      <Route path=\"/universities\" element={<Universities />} />\r\n      <Route path=\"/university-info/:universityId\" element={<UniversityInfo />} />\r\n      <Route path=\"/university-book\" element={<UniversityBook />} />\r\n      <Route path=\"/university-confirmation\" element={<UniversityConfirmation />} />\r\n      <Route path=\"/forgot-password\" element={<ForgotPassword />} />\r\n\r\n\r\n    {/* Dynamic Dashboard Redirect */}\r\n    <Route path=\"/dashboard\" element={<DashboardRedirect />} />\r\n\r\n    {/* Student Routes */}\r\n    <Route path=\"/student/dashboard\" element={<StudentRoute><StudentDashboard /></StudentRoute>} />\r\n    <Route path=\"/calendar\" element={<StudentRoute><StudentCalendar /></StudentRoute>} />\r\n    <Route path=\"/patients\" element={<StudentRoute><StudentPatients /></StudentRoute>} />\r\n    {/* <Route path=\"/messages\" element={<StudentRoute><StudentMessages /></StudentRoute>} /> */}\r\n    <Route path=\"/analytics\" element={<StudentRoute><StudentAnalytics /></StudentRoute>} />\r\n    <Route path=\"/reviews\" element={<StudentRoute><StudentReviews /></StudentRoute>} />\r\n    <Route path=\"/lab\" element={<StudentRoute><StudentLab /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId\" element={<StudentRoute><StudentPatientProfile /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId/gallery\" element={<StudentRoute><StudentGallery /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId/xrays\" element={<Navigate to=\"../gallery\" replace />} />\r\n    <Route path=\"/patientprofile/:nationalId/toothchart\" element={<StudentRoute><StudentToothChart /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId/appointments\" element={<StudentRoute><StudentAppointments /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId/reviewsteps\" element={<StudentRoute><StudentReviewSteps /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId/sheets\" element={<StudentRoute><StudentSheets /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId/history\" element={<StudentRoute><StudentHistory /></StudentRoute>} />\r\n    <Route path=\"/patientprofile/:nationalId/consent\" element={<StudentRoute><StudentConsent /></StudentRoute>} />\r\n\r\n    <Route path=\"/profile\" element={<Profile />} />\r\n    <Route path=\"/support\" element={<ProtectedRoute><Support /></ProtectedRoute>} />\r\n\r\n\r\n\r\n    {/* Supervisor Routes */}\r\n    <Route path=\"/supervisor/dashboard\" element={<SupervisorRoute><SupervisorDashboard /></SupervisorRoute>} />\r\n\r\n\r\n    {/* Admin Routes */}\r\n    <Route path=\"/admin/dashboard\" element={<AdminRoute><AdminDashboard /></AdminRoute>} />\r\n    <Route path=\"/admin/people\" element={<AdminRoute><AdminPeople /></AdminRoute>} />\r\n    <Route path=\"/admin/appointments\" element={<AdminRoute><AdminAppointments /></AdminRoute>} />\r\n    <Route path=\"/admin/analytics\" element={<AdminRoute><AdminAnalytics /></AdminRoute>} />\r\n    <Route path=\"/admin/news\" element={<AdminRoute><AdminNews /></AdminRoute>} />\r\n    <Route path=\"/admin/reviews\" element={<AdminRoute><AdminReviews /></AdminRoute>} />\r\n\r\n     {/* SuperAdmin Routes */}\r\n    <Route path=\"/superadmin/dashboard\" element={<SuperadminRoute><SuperadminDashboard /></SuperadminRoute>} />\r\n    <Route path=\"/superadmin/universities\" element={<SuperadminRoute><SuperadminUniversities /></SuperadminRoute>} />\r\n    <Route path=\"/superadmin/analytics\" element={<SuperadminRoute><SuperadminAnalytics /></SuperadminRoute>} />\r\n    <Route path=\"/superadmin/activity\" element={<SuperadminRoute><SuperadminActivity /></SuperadminRoute>} />\r\n    <Route path=\"/superadmin/news\" element={<SuperadminRoute><SuperadminNews /></SuperadminRoute>} />\r\n    <Route path=\"/superadmin/accounts\" element={<SuperadminRoute><SuperadminAccounts /></SuperadminRoute>} />\r\n\r\n    {/* Assistant Routes */}\r\n    <Route path=\"/assistant/dashboard\" element={<AssistantRoute><AssistantDashboard /></AssistantRoute>} />\r\n    <Route path=\"/assistant/appointments\" element={<AssistantRoute><AssistantAppointments /></AssistantRoute>} />\r\n    <Route path=\"/assistant/procedure-requests\" element={<AssistantRoute><AssistantProcedureRequests /></AssistantRoute>} />\r\n    <Route path=\"/assistant/analytics\" element={<AssistantRoute><AssistantAnalytics /></AssistantRoute>} />\r\n    <Route path=\"/assistant/patients\" element={<AssistantRoute><AssistantPatients /></AssistantRoute>} />\r\n\r\n    {/* 404 */}\r\n    <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\r\n  </Routes>\r\n);\r\n\r\nexport default AppRoutes;"], "mappings": ";;;;;;;;AAAA,SAASA,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,OAAO,mBAAmB;AAC1B;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAO,eAAe;AAClC,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,sBAAsB,MAAM,gCAAgC;AACnE,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C;AACA,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,qBAAqB,MAAM,0BAA0B;AAC5D,OAAOC,cAAc,MAAM,mBAAmB;AAC9C;AACA,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,mBAAmB,MAAM,wBAAwB;AACxD,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,UAAU,MAAM,eAAe;AACtC;AACA,OAAOC,mBAAmB,MAAM,wBAAwB;AACxD;AACA,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C;AACA,OAAOC,mBAAmB,MAAM,wBAAwB;AACxD,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAOC,mBAAmB,MAAM,wBAAwB;AACxD,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,qBAAqB,MAAM,0BAA0B;AAC5D,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,0BAA0B,MAAM,+BAA+B;AACtE,OAAOC,iBAAiB,MAAM,sBAAsB;;AAGpD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,IAAI,CAACmD,IAAI,EAAE,oBAAOJ,OAAA,CAAChD,QAAQ;IAACqD,EAAE,EAAC,QAAQ;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAClD,OAAOR,QAAQ;AACjB,CAAC;;AAED;AAAAC,EAAA,CANMF,cAAc;EAAA,QACDhD,OAAO;AAAA;AAAA0D,EAAA,GADpBV,cAAc;AAOpB,MAAMW,YAAY,GAAGA,CAAC;EAAEV;AAAS,CAAC,kBAAKF,OAAA,CAACC,cAAc;EAAAC,QAAA,eAACF,OAAA,CAACa,YAAY;IAAAX,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAe;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAgB,CAAC;AAACI,GAAA,GAA1GF,YAAY;AAClB,MAAMG,eAAe,GAAGA,CAAC;EAAEb;AAAS,CAAC,kBAAKF,OAAA,CAACC,cAAc;EAAAC,QAAA,eAACF,OAAA,CAACgB,eAAe;IAAAd,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAkB;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAgB,CAAC;AAACO,GAAA,GAAnHF,eAAe;AACrB,MAAMG,UAAU,GAAGA,CAAC;EAAEhB;AAAS,CAAC,kBAAKF,OAAA,CAACC,cAAc;EAAAC,QAAA,eAACF,OAAA,CAACmB,UAAU;IAAAjB,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAa;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAgB,CAAC;AAACU,GAAA,GAApGF,UAAU;AAChB,MAAMG,eAAe,GAAGA,CAAC;EAAEnB;AAAS,CAAC,kBAAKF,OAAA,CAACC,cAAc;EAAAC,QAAA,eAACF,OAAA,CAACsB,eAAe;IAAApB,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAkB;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAgB,CAAC;AAACa,GAAA,GAAnHF,eAAe;AACrB,MAAMG,cAAc,GAAGA,CAAC;EAAEtB;AAAS,CAAC,kBAAKF,OAAA,CAACC,cAAc;EAAAC,QAAA,eAACF,OAAA,CAACyB,cAAc;IAAAvB,QAAA,EAAEA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAiB;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAgB,CAAC;;AAErH;AAAAgB,GAAA,GAFMF,cAAc;AAGpB,MAAMX,YAAY,GAAGA,CAAC;EAAEX;AAAS,CAAC,KAAK;EAAAyB,GAAA;EACrC,MAAM;IAAEvB;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,OAAOmD,IAAI,CAACwB,IAAI,KAAK,SAAS,GAAG1B,QAAQ,gBAAGF,OAAA,CAAChD,QAAQ;IAACqD,EAAE,EAAC,YAAY;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAClF,CAAC;AAACiB,GAAA,CAHId,YAAY;EAAA,QACC5D,OAAO;AAAA;AAAA4E,GAAA,GADpBhB,YAAY;AAIlB,MAAMG,eAAe,GAAGA,CAAC;EAAEd;AAAS,CAAC,KAAK;EAAA4B,GAAA;EACxC,MAAM;IAAE1B;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,OAAOmD,IAAI,CAACwB,IAAI,KAAK,YAAY,GAAG1B,QAAQ,gBAAGF,OAAA,CAAChD,QAAQ;IAACqD,EAAE,EAAC,YAAY;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACrF,CAAC;AAACoB,GAAA,CAHId,eAAe;EAAA,QACF/D,OAAO;AAAA;AAAA8E,GAAA,GADpBf,eAAe;AAIrB,MAAMG,UAAU,GAAGA,CAAC;EAAEjB;AAAS,CAAC,KAAK;EAAA8B,GAAA;EACnC,MAAM;IAAE5B;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,OAAOmD,IAAI,CAACwB,IAAI,KAAK,OAAO,GAAG1B,QAAQ,gBAAGF,OAAA,CAAChD,QAAQ;IAACqD,EAAE,EAAC,YAAY;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAChF,CAAC;AAACsB,GAAA,CAHIb,UAAU;EAAA,QACGlE,OAAO;AAAA;AAAAgF,GAAA,GADpBd,UAAU;AAIhB,MAAMG,eAAe,GAAGA,CAAC;EAAEpB;AAAS,CAAC,KAAK;EAAAgC,GAAA;EACxC,MAAM;IAAE9B;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,OAAOmD,IAAI,CAACwB,IAAI,KAAK,YAAY,GAAG1B,QAAQ,gBAAGF,OAAA,CAAChD,QAAQ;IAACqD,EAAE,EAAC,YAAY;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACrF,CAAC;AAACwB,GAAA,CAHIZ,eAAe;EAAA,QACFrE,OAAO;AAAA;AAAAkF,IAAA,GADpBb,eAAe;AAIrB,MAAMG,cAAc,GAAGA,CAAC;EAAEvB;AAAS,CAAC,KAAK;EAAAkC,GAAA;EACvC,MAAM;IAAEhC;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,OAAOmD,IAAI,CAACwB,IAAI,KAAK,WAAW,GAAG1B,QAAQ,gBAAGF,OAAA,CAAChD,QAAQ;IAACqD,EAAE,EAAC,YAAY;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpF,CAAC;;AAED;AAAA0B,GAAA,CALMX,cAAc;EAAA,QACDxE,OAAO;AAAA;AAAAoF,IAAA,GADpBZ,cAAc;AAMpB,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAM;IAAEnC;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,IAAI,CAACmD,IAAI,EAAE,oBAAOJ,OAAA,CAAChD,QAAQ;IAACqD,EAAE,EAAC,QAAQ;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAElD,QAAQN,IAAI,CAACwB,IAAI;IACf,KAAK,SAAS;MACZ,oBAAO5B,OAAA,CAAChD,QAAQ;QAACqD,EAAE,EAAC,oBAAoB;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACrD,KAAK,YAAY;MACf,oBAAOV,OAAA,CAAChD,QAAQ;QAACqD,EAAE,EAAC,uBAAuB;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACxD,KAAK,OAAO;MACV,oBAAOV,OAAA,CAAChD,QAAQ;QAACqD,EAAE,EAAC,kBAAkB;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACnD,KAAK,YAAY;MACf,oBAAOV,OAAA,CAAChD,QAAQ;QAACqD,EAAE,EAAC,uBAAuB;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACxD,KAAK,WAAW;MACd,oBAAOV,OAAA,CAAChD,QAAQ;QAACqD,EAAE,EAAC,sBAAsB;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAEvD;MACE,oBAAOV,OAAA,CAAChD,QAAQ;QAACqD,EAAE,EAAC,GAAG;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAE;EACxC;AACF,CAAC;AAAC6B,GAAA,CAnBID,iBAAiB;EAAA,QACJrF,OAAO;AAAA;AAAAuF,IAAA,GADpBF,iBAAiB;AAqBvB,MAAMG,SAAS,GAAGA,CAAA,kBAChBzC,OAAA,CAAClD,MAAM;EAAAoD,QAAA,gBAELF,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,GAAG;IAACC,OAAO,eAAE3C,OAAA,CAAC9C,IAAI;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACnCV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,QAAQ;IAACC,OAAO,eAAE3C,OAAA,CAAC7C,KAAK;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC3CV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,qBAAqB;IAACC,OAAO,eAAE3C,OAAA,CAAC5C,kBAAkB;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACrEV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,UAAU;IAACC,OAAO,eAAE3C,OAAA,CAAC3C,OAAO;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/CV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,QAAQ;IAACC,OAAO,eAAE3C,OAAA,CAAC1C,KAAK;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC3CV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,SAAS;IAACC,OAAO,eAAE3C,OAAA,CAACzC,KAAK;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC5CV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,eAAe;IAACC,OAAO,eAAE3C,OAAA,CAACxC,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACzDV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,gCAAgC;IAACC,OAAO,eAAE3C,OAAA,CAACvC,cAAc;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC5EV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,kBAAkB;IAACC,OAAO,eAAE3C,OAAA,CAACtC,cAAc;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9DV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,0BAA0B;IAACC,OAAO,eAAE3C,OAAA,CAACrC,sBAAsB;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9EV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,kBAAkB;IAACC,OAAO,eAAE3C,OAAA,CAACnC,cAAc;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAIhEV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,YAAY;IAACC,OAAO,eAAE3C,OAAA,CAACsC,iBAAiB;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAG3DV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,oBAAoB;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAACjC,gBAAgB;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/FV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,WAAW;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAAChC,eAAe;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACrFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,WAAW;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAAC/B,eAAe;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAErFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,YAAY;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAAC9B,gBAAgB;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACvFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,UAAU;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAAC7B,cAAc;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACnFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,MAAM;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAACpB,UAAU;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC3EV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,6BAA6B;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAAC5B,qBAAqB;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC7GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,qCAAqC;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAAC3B,cAAc;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,mCAAmC;IAACC,OAAO,eAAE3C,OAAA,CAAChD,QAAQ;MAACqD,EAAE,EAAC,YAAY;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjGV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,wCAAwC;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAAC1B,iBAAiB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACpHV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,0CAA0C;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAACzB,mBAAmB;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACxHV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,yCAAyC;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAACxB,kBAAkB;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACtHV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,oCAAoC;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAACvB,aAAa;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC5GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,qCAAqC;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAACtB,cAAc;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,qCAAqC;IAACC,OAAO,eAAE3C,OAAA,CAACY,YAAY;MAAAV,QAAA,eAACF,OAAA,CAACrB,cAAc;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAE9GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,UAAU;IAACC,OAAO,eAAE3C,OAAA,CAACpC,OAAO;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC/CV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,UAAU;IAACC,OAAO,eAAE3C,OAAA,CAACC,cAAc;MAAAC,QAAA,eAACF,OAAA,CAAClC,OAAO;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAKhFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,uBAAuB;IAACC,OAAO,eAAE3C,OAAA,CAACe,eAAe;MAAAb,QAAA,eAACF,OAAA,CAACnB,mBAAmB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAI3GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,kBAAkB;IAACC,OAAO,eAAE3C,OAAA,CAACkB,UAAU;MAAAhB,QAAA,eAACF,OAAA,CAAClB,cAAc;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACvFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,eAAe;IAACC,OAAO,eAAE3C,OAAA,CAACkB,UAAU;MAAAhB,QAAA,eAACF,OAAA,CAACjB,WAAW;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,qBAAqB;IAACC,OAAO,eAAE3C,OAAA,CAACkB,UAAU;MAAAhB,QAAA,eAACF,OAAA,CAAChB,iBAAiB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC7FV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,kBAAkB;IAACC,OAAO,eAAE3C,OAAA,CAACkB,UAAU;MAAAhB,QAAA,eAACF,OAAA,CAACf,cAAc;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACvFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,aAAa;IAACC,OAAO,eAAE3C,OAAA,CAACkB,UAAU;MAAAhB,QAAA,eAACF,OAAA,CAACd,SAAS;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC7EV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,gBAAgB;IAACC,OAAO,eAAE3C,OAAA,CAACkB,UAAU;MAAAhB,QAAA,eAACF,OAAA,CAACb,YAAY;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAGnFV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,uBAAuB;IAACC,OAAO,eAAE3C,OAAA,CAACqB,eAAe;MAAAnB,QAAA,eAACF,OAAA,CAACZ,mBAAmB;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC3GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,0BAA0B;IAACC,OAAO,eAAE3C,OAAA,CAACqB,eAAe;MAAAnB,QAAA,eAACF,OAAA,CAACX,sBAAsB;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjHV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,uBAAuB;IAACC,OAAO,eAAE3C,OAAA,CAACqB,eAAe;MAAAnB,QAAA,eAACF,OAAA,CAACV,mBAAmB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC3GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,sBAAsB;IAACC,OAAO,eAAE3C,OAAA,CAACqB,eAAe;MAAAnB,QAAA,eAACF,OAAA,CAACP,kBAAkB;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACzGV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,kBAAkB;IAACC,OAAO,eAAE3C,OAAA,CAACqB,eAAe;MAAAnB,QAAA,eAACF,OAAA,CAACT,cAAc;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACjGV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,sBAAsB;IAACC,OAAO,eAAE3C,OAAA,CAACqB,eAAe;MAAAnB,QAAA,eAACF,OAAA,CAACR,kBAAkB;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAGzGV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,sBAAsB;IAACC,OAAO,eAAE3C,OAAA,CAACwB,cAAc;MAAAtB,QAAA,eAACF,OAAA,CAACN,kBAAkB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACvGV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,yBAAyB;IAACC,OAAO,eAAE3C,OAAA,CAACwB,cAAc;MAAAtB,QAAA,eAACF,OAAA,CAACL,qBAAqB;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC7GV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,+BAA+B;IAACC,OAAO,eAAE3C,OAAA,CAACwB,cAAc;MAAAtB,QAAA,eAACF,OAAA,CAACH,0BAA0B;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACxHV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,sBAAsB;IAACC,OAAO,eAAE3C,OAAA,CAACwB,cAAc;MAAAtB,QAAA,eAACF,OAAA,CAACJ,kBAAkB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACvGV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,qBAAqB;IAACC,OAAO,eAAE3C,OAAA,CAACwB,cAAc;MAAAtB,QAAA,eAACF,OAAA,CAACF,iBAAiB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAGrGV,OAAA,CAACjD,KAAK;IAAC2F,IAAI,EAAC,GAAG;IAACC,OAAO,eAAE3C,OAAA,CAAChD,QAAQ;MAACqD,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClD,CACT;AAACkC,IAAA,GAxEIH,SAAS;AA0Ef,eAAeA,SAAS;AAAC,IAAA9B,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAI,IAAA;AAAAC,YAAA,CAAAlC,EAAA;AAAAkC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAV,IAAA;AAAAU,YAAA,CAAAR,IAAA;AAAAQ,YAAA,CAAAL,IAAA;AAAAK,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}