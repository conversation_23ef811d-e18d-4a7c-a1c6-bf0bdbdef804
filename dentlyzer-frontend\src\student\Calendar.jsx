import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import format from 'date-fns/format';
import startOfMonth from 'date-fns/startOfMonth';
import endOfMonth from 'date-fns/endOfMonth';
import eachDayOfInterval from 'date-fns/eachDayOfInterval';
import isSameMonth from 'date-fns/isSameMonth';
import isSameDay from 'date-fns/isSameDay';
import addMonths from 'date-fns/addMonths';
import subMonths from 'date-fns/subMonths';
import parseISO from 'date-fns/parseISO';
import Loader from '../components/Loader';
import { FaCalendarAlt, FaUser, FaPlus, FaChevronLeft, FaChevronRight, FaClock, FaStethoscope, FaNotesMedical, FaCalendar, FaTimes } from 'react-icons/fa';

const Calendar = () => {
  const { user, token } = useAuth();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showModal, setShowModal] = useState(false);
  const [appointments, setAppointments] = useState([]);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [slotsLoading, setSlotsLoading] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const [newAppointment, setNewAppointment] = useState({
    date: '',
    time: '',
    type: 'checkup',
    notes: '',
    status: 'pending',
    patient: '',
    doctor: user?.studentId || '',
    doctorModel: 'Student',
    chiefComplaint: '',
  });

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Fetch all student appointments
  useEffect(() => {
    const fetchAppointments = async () => {
      if (!user?.studentId || !token) {
        setError('Please log in as a student to access the calendar.');
        setLoading(false);
        return;
      }
      try {
        setLoading(true);
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const response = await axios.get('http://localhost:5000/api/appointments/my-appointments', config);
        setAppointments(response.data.map(appt => ({
          ...appt,
          date: parseISO(appt.date),
        })));
      } catch (err) {
        console.error('Error fetching appointments:', err.response?.data || err.message);
        setError(err.response?.data?.message || 'Failed to fetch appointments');
      } finally {
        setLoading(false);
      }
    };
    fetchAppointments();
  }, [user?.studentId, token]);

  // Fetch available slots
  useEffect(() => {
    const fetchAvailableSlots = async () => {
      if (!newAppointment.date || !user?.studentId) {
        console.log('Skipping fetch: missing date or studentId', {
          date: newAppointment.date,
          studentId: user?.studentId,
        });
        setAvailableSlots([]);
        return;
      }

      setSlotsLoading(true);
      try {
        const formattedDate = new Date(newAppointment.date).toISOString().split('T')[0];
        console.log('Fetching slots with params:', {
          doctorId: user.studentId,
          doctorModel: 'Student',
          date: formattedDate,
        });

        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/available-slots`, {
          params: {
            doctorId: user.studentId,
            doctorModel: 'Student',
            date: formattedDate,
          },
          headers: { Authorization: `Bearer ${token}` },
        });

        console.log('Slots fetched:', response.data);
        setAvailableSlots(response.data || []);
      } catch (err) {
        console.error('Error fetching slots:', err.response?.data || err.message);
        setError(err.response?.data?.message || 'Failed to load available slots');
        setAvailableSlots([]);
      } finally {
        setSlotsLoading(false);
      }
    };
    fetchAvailableSlots();
  }, [newAppointment.date, user?.studentId, token]);

  const validateForm = () => {
    if (!newAppointment.date) return 'Date is required';
    if (!newAppointment.time) return 'Time is required';
    if (!newAppointment.type) return 'Appointment type is required';
    if (!newAppointment.chiefComplaint) return 'Chief complaint is required';
    if (!newAppointment.doctor) return 'Doctor ID is required';
    if (!newAppointment.patient) return 'Patient ID is required';
    return '';
  };

  const handleAddAppointment = () => {
    setShowModal(true);
    setError('');
    setNewAppointment({
      date: format(selectedDate, 'yyyy-MM-dd'),
      time: '',
      type: 'checkup',
      notes: '',
      status: 'pending',
      patient: '',
      doctor: user?.studentId || '',
      doctorModel: 'Student',
      chiefComplaint: '',
    });
  };

  const handleSubmitAppointment = async (e) => {
    e.preventDefault();
    if (!user?.studentId) {
      setError('You must be logged in as a student to add an appointment.');
      return;
    }
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }
    try {
      console.log('Creating appointment:', newAppointment);
      const response = await axios.post('http://localhost:5000/api/appointments', newAppointment, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log('Appointment created:', response.data);
      setAppointments([...appointments, { ...response.data, date: parseISO(response.data.date) }]);
      setNewAppointment({
        date: '',
        time: '',
        type: 'checkup',
        notes: '',
        status: 'pending',
        patient: '',
        doctor: user.studentId,
        doctorModel: 'Student',
        chiefComplaint: '',
      });
      setShowModal(false);
      setError('');
    } catch (err) {
      console.error('Error creating appointment:', err.response?.data || err.message);
      setError(err.response?.data?.message || 'Failed to add appointment');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log('Input changed:', { name, value });
    setNewAppointment({ ...newAppointment, [name]: value });
  };

  const handlePrevMonth = () => setCurrentDate(subMonths(currentDate, 1));
  const handleNextMonth = () => setCurrentDate(addMonths(currentDate, 1));
  const handleDateClick = (day) => {
    setSelectedDate(day);
    setNewAppointment({ ...newAppointment, date: format(day, 'yyyy-MM-dd') });
  };
  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const getAppointmentsForDay = (day) => appointments.filter((appt) => isSameDay(appt.date, day));

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  if (!user || !user.studentId) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={toggleSidebar} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-blue-50 to-white">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100"
              >
                <div className="text-red-500 mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Access Denied</h3>
                <p className="text-gray-600 mb-6">Please log in as a student to view this page.</p>
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={toggleSidebar} />
        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">Appointment Calendar</h1>
                  <p className="text-[#333333]">Manage your schedule, {user?.name || 'Student'}</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleAddAppointment}
                  className="w-full sm:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <FaPlus className="h-5 w-5 mr-2" />
                  Add Appointment
                </motion.button>
              </div>

              {error && !showModal && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    <p className="text-red-700 font-medium">{error}</p>
                  </div>
                </motion.div>
              )}

              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-6"
              >
                {/* Calendar Section */}
                <motion.div
                  variants={item}
                  className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] flex flex-col"
                >
                  <div className="flex items-center justify-between mb-6">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={handlePrevMonth}
                      className="p-2 text-[#0077B6] hover:bg-[rgba(0,119,182,0.05)] rounded-full"
                    >
                      <FaChevronLeft className="h-5 w-5" />
                    </motion.button>
                    <h2 className="text-xl font-bold text-[#0077B6]">{format(currentDate, 'MMMM yyyy')}</h2>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={handleNextMonth}
                      className="p-2 text-[#0077B6] hover:bg-[rgba(0,119,182,0.05)] rounded-full"
                    >
                      <FaChevronRight className="h-5 w-5" />
                    </motion.button>
                  </div>
                  <div className="grid grid-cols-7 gap-2 mb-4">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                      <div key={day} className="text-center text-sm font-semibold text-gray-500">
                        {day}
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-7 gap-2">
                    {daysInMonth.map((day, i) => {
                      const dayAppointments = getAppointmentsForDay(day);
                      const isSelected = isSameDay(day, selectedDate);
                      const isCurrentMonth = isSameMonth(day, currentDate);
                      const isToday = isSameDay(day, new Date());

                      return (
                        <motion.div
                          key={i}
                          onClick={() => handleDateClick(day)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className={`relative p-2 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center ${
                            isSelected ? 'bg-[rgba(0,119,182,0.1)] border-2 border-[#0077B6] shadow-inner' :
                            isToday ? 'bg-[rgba(0,119,182,0.05)] border border-[#20B2AA]' :
                            'hover:bg-[rgba(0,119,182,0.05)] border border-gray-200'
                          } ${!isCurrentMonth ? 'opacity-60' : ''}`}
                        >
                          <span className={`text-sm font-medium ${
                            isSelected ? 'text-[#0077B6]' : isToday ? 'text-[#20B2AA]' : 'text-[#333333]'
                          }`}>
                            {format(day, 'd')}
                          </span>
                          {dayAppointments.length > 0 && (
                            <span className="absolute -top-1 -right-1 h-4 w-4 bg-[#0077B6] text-white text-xs rounded-full flex items-center justify-center">
                              {dayAppointments.length}
                            </span>
                          )}
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>

                {/* Appointments for Selected Date */}
                <motion.div
                  variants={item}
                  className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#20B2AA] flex flex-col"
                >
                  <h2 className="text-xl font-bold text-[#0077B6] mb-6 flex items-center">
                    <FaCalendarAlt className="h-5 w-5 mr-2 text-[#20B2AA]" />
                    {format(selectedDate, 'MMMM d, yyyy')}
                  </h2>
                  {getAppointmentsForDay(selectedDate).length > 0 ? (
                    <div className="space-y-4 overflow-y-auto flex-1">
                      {getAppointmentsForDay(selectedDate).map((appt) => (
                        <motion.div
                          key={appt._id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="p-4 bg-[rgba(0,119,182,0.05)] border border-[rgba(0,119,182,0.1)] rounded-lg hover:bg-[rgba(0,119,182,0.1)] transition-colors"
                        >
                          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                            <div className="flex items-center space-x-3">
                              <div className="bg-[rgba(0,119,182,0.1)] p-2 rounded-lg">
                                <FaStethoscope className="h-5 w-5 text-[#0077B6]" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{appt.type}</h3>
                                <p className="text-sm text-gray-600 flex items-center">
                                  <FaUser className="h-3 w-3 mr-1" /> {appt.patient?.fullName || 'Unknown'}
                                </p>
                                <p className="text-sm text-gray-600 flex items-center">
                                  <FaNotesMedical className="h-3 w-3 mr-1" /> {appt.chiefComplaint}
                                </p>
                              </div>
                            </div>
                            <span className="mt-2 sm:mt-0 text-sm font-medium text-[#0077B6] bg-[rgba(0,119,182,0.1)] px-3 py-1 rounded-full flex items-center">
                              <FaClock className="h-3 w-3 mr-1" /> {appt.time}
                            </span>
                          </div>
                          {appt.notes && (
                            <div className="mt-2 text-sm text-gray-500 flex items-start">
                              <FaNotesMedical className="h-3 w-3 mt-1 mr-1 flex-shrink-0 text-[#20B2AA]" />
                              <span>{appt.notes}</span>
                            </div>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900">No appointments</h3>
                        <p className="mt-1 text-gray-500">Schedule a new appointment to get started.</p>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleAddAppointment}
                          className="mt-4 px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg"
                        >
                          Add Appointment
                        </motion.button>
                      </div>
                    </div>
                  )}
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Add Appointment Modal */}
            {showModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <motion.div
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="bg-white rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100"
                >
                  <div className="p-6 border-b flex justify-between items-center bg-[rgba(0,119,182,0.05)]">
                    <h2 className="text-xl font-semibold text-[#0077B6]">New Appointment</h2>
                    <button
                      onClick={() => setShowModal(false)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <FaTimes className="h-6 w-6" />
                    </button>
                  </div>
                  <form onSubmit={handleSubmitAppointment} className="p-6 space-y-6">
                    {error && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="mb-4 p-3 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
                      >
                        {error}
                      </motion.div>
                    )}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Date*</label>
                      <div className="relative">
                        <FaCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="date"
                          name="date"
                          value={newAppointment.date}
                          onChange={handleInputChange}
                          className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Time*</label>
                      <div className="relative">
                        <FaClock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <select
                          name="time"
                          value={newAppointment.time}
                          onChange={handleInputChange}
                          className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                          required
                          disabled={slotsLoading || !newAppointment.date}
                        >
                          <option value="">Select a time</option>
                          {slotsLoading ? (
                            <option value="" disabled>Loading slots...</option>
                          ) : availableSlots.length === 0 ? (
                            <option value="" disabled>No available slots</option>
                          ) : (
                            availableSlots.map((slot) => (
                              <option key={slot} value={slot.split(' - ')[0]}>
                                {slot}
                              </option>
                            ))
                          )}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Type*</label>
                      <select
                        name="type"
                        value={newAppointment.type}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                        required
                      >
                        <option value="checkup">Checkup</option>
                        <option value="cleaning">Cleaning</option>
                        <option value="filling">Filling</option>
                        <option value="extraction">Extraction</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Chief Complaint*</label>
                      <textarea
                        name="chiefComplaint"
                        value={newAppointment.chiefComplaint}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                        rows="3"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Notes (Optional)</label>
                      <textarea
                        name="notes"
                        value={newAppointment.notes}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                        rows="3"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Doctor (Student ID)*</label>
                      <input
                        type="text"
                        name="doctor"
                        value={newAppointment.doctor}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed"
                        disabled
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Patient ID*</label>
                      <input
                        type="text"
                        name="patient"
                        value={newAppointment.patient}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                        placeholder="Enter patient national ID"
                        required
                      />
                    </div>
                    <div className="flex justify-end space-x-4">
                      <motion.button
                        type="button"
                        onClick={() => setShowModal(false)}
                        className="px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        type="submit"
                        className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        disabled={slotsLoading}
                      >
                        Save
                      </motion.button>
                    </div>
                  </form>
                </motion.div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Calendar;
