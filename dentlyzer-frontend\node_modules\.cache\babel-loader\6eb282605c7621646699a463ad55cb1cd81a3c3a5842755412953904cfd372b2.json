{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\PatientNav.jsx\",\n  _s = $RefreshSig$();\nimport { NavLink, useParams } from 'react-router-dom';\nimport { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaFileAlt, FaHistory, FaTooth, FaImages, FaCalendarAlt, FaFileSignature, FaClipboardCheck, FaFlask } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientNav = ({\n  selectedChart,\n  setSelectedChart,\n  charts\n}) => {\n  _s();\n  const {\n    nationalId\n  } = useParams();\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);\n        setPatientData(response.data);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data;\n        console.error('Error fetching patient data:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status, (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data);\n        const message = ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 404 ? 'Patient not found' : ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to load patient data';\n        setError(message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPatientData();\n  }, [nationalId]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-100 px-4 py-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rounded-full bg-gray-200 h-10 w-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-32\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-24\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !patientData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-100 px-4 py-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500\",\n          children: error || 'Patient not found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this);\n  }\n  const renderChartSelector = isActive => {\n    if (!isActive || !charts || charts.length === 0) return null;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"ml-2\",\n      children: /*#__PURE__*/_jsxDEV(\"select\", {\n        value: (selectedChart === null || selectedChart === void 0 ? void 0 : selectedChart._id) || '',\n        onChange: e => setSelectedChart(charts.find(chart => chart._id === e.target.value)),\n        className: \"px-3 py-1.5 rounded-lg bg-white border border-gray-200 text-sm text-gray-600 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-200\",\n        children: charts.map(chart => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: chart._id,\n          children: [chart.title, \" - \", new Date(chart.date).toLocaleDateString(), \" \", chart.isLocked ? '(Locked)' : '']\n        }, chart._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(motion.nav, {\n    initial: {\n      opacity: 0,\n      y: -20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      duration: 0.3\n    },\n    className: \"bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)] px-4 py-3 overflow-x-auto w-full\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center justify-between gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(NavLink, {\n        to: `/patientprofile/${nationalId}`,\n        className: \"flex items-center w-full md:w-auto hover:opacity-80 transition-opacity\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-[rgba(0,119,182,0.1)] p-2 rounded-lg mr-3 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-6 w-6 text-[#0077B6]\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xs font-medium text-gray-500 uppercase tracking-wider\",\n            children: \"Current Patient\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-semibold text-gray-800 truncate\",\n            children: patientData.fullName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full md:w-auto mx-auto flex flex-col md:flex-row items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"flex flex-wrap justify-center gap-1 md:gap-2 w-full overflow-x-auto py-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              to: `/patientprofile/${nationalId}/sheets`,\n              className: ({\n                isActive\n              }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-1 font-medium\",\n                children: \"Patient Examination Sheet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              to: `/patientprofile/${nationalId}/history`,\n              className: ({\n                isActive\n              }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(FaHistory, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-1 font-medium\",\n                children: \"History\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(NavLink, {\n              to: `/patientprofile/${nationalId}/toothchart`,\n              className: ({\n                isActive\n              }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(FaTooth, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-1 font-medium\",\n                children: \"Tooth Chart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), renderChartSelector(window.location.pathname.includes('/toothchart'))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              to: `/patientprofile/${nationalId}/gallery`,\n              className: ({\n                isActive\n              }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(FaImages, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-1 font-medium\",\n                children: \"Gallery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              to: `/patientprofile/${nationalId}/appointments`,\n              className: ({\n                isActive\n              }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-1 font-medium\",\n                children: \"Appointments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              to: `/patientprofile/${nationalId}/consent`,\n              className: ({\n                isActive\n              }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(FaFileSignature, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-1 font-medium\",\n                children: \"Consent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(NavLink, {\n              to: `/patientprofile/${nationalId}/reviewsteps`,\n              className: ({\n                isActive\n              }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs mt-1 font-medium\",\n                children: \"Review Steps\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:block w-48\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientNav, \"jqEn2v0+dcCU8CrZ1NEhObZY6q4=\", false, function () {\n  return [useParams];\n});\n_c = PatientNav;\nexport default PatientNav;\nvar _c;\n$RefreshReg$(_c, \"PatientNav\");", "map": {"version": 3, "names": ["NavLink", "useParams", "useState", "useEffect", "axios", "motion", "FaFileAlt", "FaHistory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaImages", "FaCalendarAlt", "FaFileSignature", "FaClipboardCheck", "FaFlask", "jsxDEV", "_jsxDEV", "PatientNav", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "charts", "_s", "nationalId", "patientData", "setPatientData", "loading", "setLoading", "error", "setError", "fetchPatientData", "response", "get", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "console", "status", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderChartSelector", "isActive", "length", "div", "initial", "opacity", "y", "animate", "value", "_id", "onChange", "e", "find", "chart", "target", "map", "title", "Date", "date", "toLocaleDateString", "isLocked", "nav", "transition", "duration", "to", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fullName", "window", "location", "pathname", "includes", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/PatientNav.jsx"], "sourcesContent": ["import { NavLink, useParams } from 'react-router-dom';\r\nimport { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { motion } from 'framer-motion';\r\nimport { FaFileAlt, FaHistory, FaTooth, FaImages, FaCalendarAlt, FaFileSignature, FaClipboardCheck, FaFlask } from 'react-icons/fa';\r\n\r\nconst PatientNav = ({ selectedChart, setSelectedChart, charts }) => {\r\n  const { nationalId } = useParams();\r\n  const [patientData, setPatientData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n\r\n  useEffect(() => {\r\n    const fetchPatientData = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);\r\n        setPatientData(response.data);\r\n      } catch (err) {\r\n        console.error('Error fetching patient data:', err.response?.status, err.response?.data);\r\n        const message = err.response?.status === 404\r\n          ? 'Patient not found'\r\n          : err.response?.data?.message || 'Failed to load patient data';\r\n        setError(message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchPatientData();\r\n  }, [nationalId]);\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"bg-white shadow-sm border-b border-gray-100 px-4 py-3\">\r\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\r\n          <div className=\"animate-pulse flex items-center space-x-4\">\r\n            <div className=\"rounded-full bg-gray-200 h-10 w-10\"></div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"h-4 bg-gray-200 rounded w-32\"></div>\r\n              <div className=\"h-4 bg-gray-200 rounded w-24\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !patientData) {\r\n    return (\r\n      <div className=\"bg-white shadow-sm border-b border-gray-100 px-4 py-3\">\r\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\r\n          <div className=\"text-red-500\">{error || 'Patient not found'}</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderChartSelector = (isActive) => {\r\n    if (!isActive || !charts || charts.length === 0) return null;\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"ml-2\"\r\n      >\r\n        <select\r\n          value={selectedChart?._id || ''}\r\n          onChange={(e) => setSelectedChart(charts.find(chart => chart._id === e.target.value))}\r\n          className=\"px-3 py-1.5 rounded-lg bg-white border border-gray-200 text-sm text-gray-600 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-200\"\r\n        >\r\n          {charts.map(chart => (\r\n            <option key={chart._id} value={chart._id}>\r\n              {chart.title} - {new Date(chart.date).toLocaleDateString()} {chart.isLocked ? '(Locked)' : ''}\r\n            </option>\r\n          ))}\r\n        </select>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <motion.nav\r\n      initial={{ opacity: 0, y: -20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n      className=\"bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)] px-4 py-3 overflow-x-auto w-full\"\r\n    >\r\n      <div className=\"max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center justify-between gap-4\">\r\n        <NavLink to={`/patientprofile/${nationalId}`} className=\"flex items-center w-full md:w-auto hover:opacity-80 transition-opacity\">\r\n          <div className=\"bg-[rgba(0,119,182,0.1)] p-2 rounded-lg mr-3 flex-shrink-0\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-6 w-6 text-[#0077B6]\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <div className=\"min-w-0\">\r\n            <h2 className=\"text-xs font-medium text-gray-500 uppercase tracking-wider\">Current Patient</h2>\r\n            <h1 className=\"text-lg font-semibold text-gray-800 truncate\">{patientData.fullName}</h1>\r\n          </div>\r\n        </NavLink>\r\n\r\n        <div className=\"w-full md:w-auto mx-auto flex flex-col md:flex-row items-center gap-2\">\r\n          <ul className=\"flex flex-wrap justify-center gap-1 md:gap-2 w-full overflow-x-auto py-2\">\r\n\r\n\r\n            {/* Sheets */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/sheets`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaFileAlt className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Patient Examination Sheet</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* History */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/history`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaHistory className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">History</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* Tooth Chart */}\r\n            <li className=\"flex items-center\">\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/toothchart`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaTooth className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Tooth Chart</span>\r\n              </NavLink>\r\n              {renderChartSelector(window.location.pathname.includes('/toothchart'))}\r\n            </li>\r\n\r\n\r\n\r\n            {/* Gallery */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/gallery`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaImages className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Gallery</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n\r\n\r\n            {/* Appointments */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/appointments`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaCalendarAlt className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Appointments</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* Consent */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/consent`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaFileSignature className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Consent</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* Review Steps */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/reviewsteps`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaClipboardCheck className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Review Steps</span>\r\n              </NavLink>\r\n            </li>\r\n          </ul>\r\n\r\n        </div>\r\n\r\n        <div className=\"hidden md:block w-48\"></div>\r\n      </div>\r\n    </motion.nav>\r\n  );\r\n};\r\n\r\nexport default PatientNav;"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,SAAS,QAAQ,kBAAkB;AACrD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpI,MAAMC,UAAU,GAAGA,CAAC;EAAEC,aAAa;EAAEC,gBAAgB;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM;IAAEC;EAAW,CAAC,GAAGpB,SAAS,CAAC,CAAC;EAClC,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,MAAMyB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,6CAA6CT,UAAU,EAAE,CAAC;QAC3FE,cAAc,CAACM,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZC,OAAO,CAACZ,KAAK,CAAC,8BAA8B,GAAAO,aAAA,GAAED,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcM,MAAM,GAAAL,cAAA,GAAEF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcH,IAAI,CAAC;QACvF,MAAMS,OAAO,GAAG,EAAAL,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcI,MAAM,MAAK,GAAG,GACxC,mBAAmB,GACnB,EAAAH,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,6BAA6B;QAChEb,QAAQ,CAACa,OAAO,CAAC;MACnB,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAIhB,IAAIG,OAAO,EAAE;IACX,oBACET,OAAA;MAAK0B,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpE3B,OAAA;QAAK0B,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE3B,OAAA;UAAK0B,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxD3B,OAAA;YAAK0B,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1D/B,OAAA;YAAK0B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3B,OAAA;cAAK0B,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD/B,OAAA;cAAK0B,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIpB,KAAK,IAAI,CAACJ,WAAW,EAAE;IACzB,oBACEP,OAAA;MAAK0B,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpE3B,OAAA;QAAK0B,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClE3B,OAAA;UAAK0B,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAEhB,KAAK,IAAI;QAAmB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,IAAI,CAACA,QAAQ,IAAI,CAAC7B,MAAM,IAAIA,MAAM,CAAC8B,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5D,oBACElC,OAAA,CAACV,MAAM,CAAC6C,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BZ,SAAS,EAAC,MAAM;MAAAC,QAAA,eAEhB3B,OAAA;QACEwC,KAAK,EAAE,CAAAtC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,GAAG,KAAI,EAAG;QAChCC,QAAQ,EAAGC,CAAC,IAAKxC,gBAAgB,CAACC,MAAM,CAACwC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACJ,GAAG,KAAKE,CAAC,CAACG,MAAM,CAACN,KAAK,CAAC,CAAE;QACtFd,SAAS,EAAC,sLAAsL;QAAAC,QAAA,EAE/LvB,MAAM,CAAC2C,GAAG,CAACF,KAAK,iBACf7C,OAAA;UAAwBwC,KAAK,EAAEK,KAAK,CAACJ,GAAI;UAAAd,QAAA,GACtCkB,KAAK,CAACG,KAAK,EAAC,KAAG,EAAC,IAAIC,IAAI,CAACJ,KAAK,CAACK,IAAI,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,GAAC,EAACN,KAAK,CAACO,QAAQ,GAAG,UAAU,GAAG,EAAE;QAAA,GADlFP,KAAK,CAACJ,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEd,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEjB,CAAC;EAED,oBACE/B,OAAA,CAACV,MAAM,CAAC+D,GAAG;IACTjB,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAChCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BgB,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAC9B7B,SAAS,EAAC,2FAA2F;IAAAC,QAAA,eAErG3B,OAAA;MAAK0B,SAAS,EAAC,+FAA+F;MAAAC,QAAA,gBAC5G3B,OAAA,CAACf,OAAO;QAACuE,EAAE,EAAE,mBAAmBlD,UAAU,EAAG;QAACoB,SAAS,EAAC,wEAAwE;QAAAC,QAAA,gBAC9H3B,OAAA;UAAK0B,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eACzE3B,OAAA;YACEyD,KAAK,EAAC,4BAA4B;YAClC/B,SAAS,EAAC,wBAAwB;YAClCgC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,MAAM,EAAC,cAAc;YAAAjC,QAAA,eAErB3B,OAAA;cACE6D,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAqE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/B,OAAA;UAAK0B,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtB3B,OAAA;YAAI0B,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/F/B,OAAA;YAAI0B,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAEpB,WAAW,CAAC0D;UAAQ;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEV/B,OAAA;QAAK0B,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpF3B,OAAA;UAAI0B,SAAS,EAAC,0EAA0E;UAAAC,QAAA,gBAItF3B,OAAA;YAAA2B,QAAA,eACE3B,OAAA,CAACf,OAAO;cACNuE,EAAE,EAAE,mBAAmBlD,UAAU,SAAU;cAC3CoB,SAAS,EAAEA,CAAC;gBAAEO;cAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;cAAAN,QAAA,gBAED3B,OAAA,CAACT,SAAS;gBAACmC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjC/B,OAAA;gBAAM0B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGL/B,OAAA;YAAA2B,QAAA,eACE3B,OAAA,CAACf,OAAO;cACNuE,EAAE,EAAE,mBAAmBlD,UAAU,UAAW;cAC5CoB,SAAS,EAAEA,CAAC;gBAAEO;cAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;cAAAN,QAAA,gBAED3B,OAAA,CAACR,SAAS;gBAACkC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjC/B,OAAA;gBAAM0B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGL/B,OAAA;YAAI0B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC/B3B,OAAA,CAACf,OAAO;cACNuE,EAAE,EAAE,mBAAmBlD,UAAU,aAAc;cAC/CoB,SAAS,EAAEA,CAAC;gBAAEO;cAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;cAAAN,QAAA,gBAED3B,OAAA,CAACP,OAAO;gBAACiC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/B/B,OAAA;gBAAM0B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,EACTC,mBAAmB,CAACkC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAC;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAKL/B,OAAA;YAAA2B,QAAA,eACE3B,OAAA,CAACf,OAAO;cACNuE,EAAE,EAAE,mBAAmBlD,UAAU,UAAW;cAC5CoB,SAAS,EAAEA,CAAC;gBAAEO;cAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;cAAAN,QAAA,gBAED3B,OAAA,CAACN,QAAQ;gBAACgC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC/B,OAAA;gBAAM0B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAKL/B,OAAA;YAAA2B,QAAA,eACE3B,OAAA,CAACf,OAAO;cACNuE,EAAE,EAAE,mBAAmBlD,UAAU,eAAgB;cACjDoB,SAAS,EAAEA,CAAC;gBAAEO;cAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;cAAAN,QAAA,gBAED3B,OAAA,CAACL,aAAa;gBAAC+B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrC/B,OAAA;gBAAM0B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGL/B,OAAA;YAAA2B,QAAA,eACE3B,OAAA,CAACf,OAAO;cACNuE,EAAE,EAAE,mBAAmBlD,UAAU,UAAW;cAC5CoB,SAAS,EAAEA,CAAC;gBAAEO;cAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;cAAAN,QAAA,gBAED3B,OAAA,CAACJ,eAAe;gBAAC8B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/B,OAAA;gBAAM0B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGL/B,OAAA;YAAA2B,QAAA,eACE3B,OAAA,CAACf,OAAO;cACNuE,EAAE,EAAE,mBAAmBlD,UAAU,cAAe;cAChDoB,SAAS,EAAEA,CAAC;gBAAEO;cAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;cAAAN,QAAA,gBAED3B,OAAA,CAACH,gBAAgB;gBAAC6B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxC/B,OAAA;gBAAM0B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEF,CAAC,eAEN/B,OAAA;QAAK0B,SAAS,EAAC;MAAsB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAC1B,EAAA,CApOIJ,UAAU;EAAA,QACSf,SAAS;AAAA;AAAAoF,EAAA,GAD5BrE,UAAU;AAsOhB,eAAeA,UAAU;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}