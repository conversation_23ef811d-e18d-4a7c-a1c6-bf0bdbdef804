import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaUser, FaCalendarAlt, FaFilter, FaDownload, FaEye } from 'react-icons/fa';
import Navbar from '../student/Navbar';
import Loader from '../components/Loader';
import { useAuth } from '../context/AuthContext';
import SuperAdminSidebar from './SuperAdminSidebar';

const Activity = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    dateRange: '7days',
    userType: 'all',
    actionType: 'all'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    fetchActivities();
  }, [filters, pagination.page]);

  const fetchActivities = async () => {
    if (!user || !token) {
      setError('Please log in to view activities.');
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const config = {
        headers: { Authorization: `Bearer ${token}` },
        params: {
          ...filters,
          page: pagination.page,
          limit: pagination.limit
        }
      };
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/activity`, config);
      setActivities(response.data.activities || []);
      setPagination(prev => ({
        ...prev,
        total: response.data.total || 0,
        totalPages: response.data.totalPages || 0
      }));
    } catch (err) {
      console.error('Fetch error:', err.response?.data || err.message);
      const errorMessage = err.response?.status === 401
        ? 'Unauthorized. Please log in again.'
        : err.response?.data?.message || 'Failed to load activities';
      setError(errorMessage);
      if (err.response?.status === 401) {
        navigate('/login');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const exportActivities = async () => {
    try {
      const config = {
        headers: { Authorization: `Bearer ${token}` },
        params: filters,
        responseType: 'blob'
      };
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/activity/export`, config);
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `activity-log-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError('Failed to export activities');
    }
  };

  const getActionIcon = (action) => {
    if (action.includes('login') || action.includes('logout')) return '🔐';
    if (action.includes('create') || action.includes('add')) return '➕';
    if (action.includes('update') || action.includes('edit')) return '✏️';
    if (action.includes('delete') || action.includes('remove')) return '🗑️';
    if (action.includes('view') || action.includes('access')) return '👁️';
    return '📝';
  };

  const getUserTypeColor = (userType) => {
    switch (userType) {
      case 'superadmin': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'supervisor': return 'bg-green-100 text-green-800';
      case 'student': return 'bg-yellow-100 text-yellow-800';
      case 'assistant': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {/* Header */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">Activity Log</h1>
                  <p className="text-[#333333]">Monitor system activities and user actions</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={exportActivities}
                  className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <FaDownload className="h-5 w-5 mr-2" />
                  Export CSV
                </motion.button>
              </div>

              {/* Filters */}
              <motion.div
                variants={item}
                className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-[rgba(0,119,182,0.1)]"
              >
                <div className="flex items-center mb-4">
                  <FaFilter className="h-5 w-5 text-[#0077B6] mr-2" />
                  <h2 className="text-lg font-semibold text-[#0077B6]">Filters</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                    <select
                      value={filters.dateRange}
                      onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    >
                      <option value="1day">Last 24 Hours</option>
                      <option value="7days">Last 7 Days</option>
                      <option value="30days">Last 30 Days</option>
                      <option value="90days">Last 90 Days</option>
                      <option value="all">All Time</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">User Type</label>
                    <select
                      value={filters.userType}
                      onChange={(e) => handleFilterChange('userType', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    >
                      <option value="all">All Users</option>
                      <option value="superadmin">Super Admins</option>
                      <option value="admin">Admins</option>
                      <option value="supervisor">Supervisors</option>
                      <option value="student">Students</option>
                      <option value="assistant">Assistants</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Action Type</label>
                    <select
                      value={filters.actionType}
                      onChange={(e) => handleFilterChange('actionType', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    >
                      <option value="all">All Actions</option>
                      <option value="auth">Authentication</option>
                      <option value="create">Create</option>
                      <option value="update">Update</option>
                      <option value="delete">Delete</option>
                      <option value="view">View</option>
                    </select>
                  </div>
                </div>
              </motion.div>

              {/* Activity List */}
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm border border-[rgba(0,119,182,0.1)] overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-[#0077B6] flex items-center">
                      <FaEye className="h-5 w-5 mr-2" />
                      Recent Activities ({pagination.total} total)
                    </h2>
                  </div>

                  {activities.length === 0 ? (
                    <div className="text-center py-12">
                      <FaUser className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900">No activities found</h3>
                      <p className="mt-1 text-gray-500">Try adjusting your filters to see more results.</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {activities.map((activity, index) => (
                        <motion.div
                          key={activity._id || index}
                          variants={item}
                          className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-[rgba(0,119,182,0.1)] rounded-full flex items-center justify-center">
                              <span className="text-lg">{getActionIcon(activity.action)}</span>
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <p className="text-sm font-medium text-gray-900">{activity.userName || 'Unknown User'}</p>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getUserTypeColor(activity.userRole)}`}>
                                  {activity.userRole || 'Unknown'}
                                </span>
                              </div>
                              <div className="flex items-center text-sm text-gray-500">
                                <FaCalendarAlt className="h-4 w-4 mr-1" />
                                {new Date(activity.timestamp).toLocaleString()}
                              </div>
                            </div>
                            <p className="mt-1 text-sm text-gray-600">{activity.action}</p>
                            {activity.details && (
                              <p className="mt-1 text-xs text-gray-500">{activity.details}</p>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}

                  {/* Pagination */}
                  {pagination.totalPages > 1 && (
                    <div className="mt-6 flex items-center justify-between">
                      <div className="text-sm text-gray-700">
                        Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page === 1}
                          className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Previous
                        </button>
                        <span className="px-3 py-2 text-sm font-medium text-[#0077B6] bg-[rgba(0,119,182,0.1)] border border-[#0077B6] rounded-md">
                          {pagination.page} of {pagination.totalPages}
                        </span>
                        <button
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page === pagination.totalPages}
                          className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Next
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Activity;
