import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import Sidebar from './Sidebar';
import Navbar from './Navbar';
import PatientNav from './PatientNav';
import { printElementAsPDF, createPDFDownloadButton, generateSheetPDF } from '../utils/pdfUtils';

const History = ({ selectedChart, setSelectedChart, charts }) => {
  const { nationalId } = useParams();
  const { token } = useAuth();
  const [sheets, setSheets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedSheet, setSelectedSheet] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All');
  const [sortOrder, setSortOrder] = useState('newest'); // 'newest' or 'oldest'

  // Sheet types for filter dropdown
  const sheetTypes = ['All', 'Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics'];

  useEffect(() => {
    const fetchSheetHistory = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/treatment-sheets`,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        setSheets(response.data);
      } catch (err) {
        console.error('Error fetching sheet history:', err.response?.status, err.response?.data);
        setError(err.response?.data?.message || 'Failed to load sheet history');
      } finally {
        setLoading(false);
      }
    };

    fetchSheetHistory();
  }, [nationalId, token]);

  const renderSheetDetails = (sheet) => {
    const { details } = sheet;

    // Function to render nested objects in a more organized way
    const renderObject = (obj, level = 0) => {
      if (!obj || typeof obj !== 'object') return null;

      // Group data into sections for better organization
      const sections = {};

      // Process object entries and group them by section
      Object.entries(obj).forEach(([key, value]) => {
        // Skip empty objects
        if (value === null || (typeof value === 'object' && Object.keys(value).length === 0)) {
          return;
        }

        // Determine section based on key naming patterns
        let section = 'General';
        if (key.includes('examination') || key.includes('Examination')) {
          section = 'Examination';
        } else if (key.includes('treatment') || key.includes('Treatment') || key.includes('Plan')) {
          section = 'Treatment Plan';
        } else if (key.includes('diagnosis') || key.includes('Diagnosis') || key.includes('assessment')) {
          section = 'Diagnosis & Assessment';
        } else if (key.includes('history') || key.includes('History')) {
          section = 'Patient History';
        }

        // Initialize section if it doesn't exist
        if (!sections[section]) {
          sections[section] = [];
        }

        // Add data to the appropriate section
        if (typeof value === 'object' && !Array.isArray(value)) {
          sections[section].push(
            <div key={key} className="mb-4">
              <h4 className="text-md font-medium text-[#0077B6] capitalize mb-2 border-b border-[#0077B6]/10 pb-1">
                {key.replace(/([A-Z])/g, ' $1').trim()}
              </h4>
              <div className="pl-4 border-l-2 border-[#0077B6]/10">
                {renderObject(value, level + 1)}
              </div>
            </div>
          );
        } else if (Array.isArray(value)) {
          // Special handling for arrays
          sections[section].push(
            <div key={key} className="mb-4">
              <h4 className="text-md font-medium text-[#0077B6] capitalize mb-2 border-b border-[#0077B6]/10 pb-1">
                {key.replace(/([A-Z])/g, ' $1').trim()}
              </h4>
              <div className="pl-4 border-l-2 border-[#0077B6]/10">
                {value.length > 0 ? (
                  <div className="space-y-3">
                    {value.map((item, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Item {index + 1}</h5>
                        {typeof item === 'object' ? (
                          renderObject(item, level + 1)
                        ) : (
                          <span className="text-sm text-gray-800">{item || '-'}</span>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <span className="text-sm text-gray-500">No items</span>
                )}
              </div>
            </div>
          );
        } else {
          sections[section].push(
            <div key={key} className="flex items-start py-1.5 group hover:bg-[#0077B6]/10 px-2 rounded-md transition-colors">
              <span className="text-sm font-medium text-gray-700 capitalize w-1/3 pr-2">
                {key.replace(/([A-Z])/g, ' $1').trim()}:
              </span>
              <span className="text-sm text-gray-800 w-2/3 font-normal group-hover:text-[#0077B6]">
                {value || '-'}
              </span>
            </div>
          );
        }
      });

      // Render each section
      return Object.entries(sections).map(([sectionName, content]) => (
        <div key={sectionName} className="mb-6">
          {level === 0 && (
            <h3 className="text-lg font-semibold text-[#0077B6] mb-3 pb-1 border-b border-gray-200">
              {sectionName}
            </h3>
          )}
          <div className="space-y-1">
            {content}
          </div>
        </div>
      ));
    };

    // Main sheet details component
    return (
      <div className="bg-white rounded-lg shadow-sm">
        {/* Basic sheet information */}
        <div className="p-5 border-b border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-[#0077B6]/10 p-4 rounded-lg">
              <h3 className="text-md font-semibold text-[#0077B6] mb-2">Diagnosis</h3>
              <p className="text-sm text-gray-800 whitespace-pre-wrap">
                {details.diagnosis || 'No diagnosis provided'}
              </p>
            </div>

            <div className="bg-[#20B2AA]/10 p-4 rounded-lg">
              <h3 className="text-md font-semibold text-[#20B2AA] mb-2">Treatment Plan</h3>
              <p className="text-sm text-gray-800 whitespace-pre-wrap">
                {details.treatmentPlan || 'No treatment plan provided'}
              </p>
            </div>
          </div>

          {details.notes && (
            <div className="mt-4 bg-[#28A745]/10 p-4 rounded-lg">
              <h3 className="text-md font-semibold text-[#28A745] mb-2">Notes</h3>
              <p className="text-sm text-gray-800 whitespace-pre-wrap">
                {details.notes}
              </p>
            </div>
          )}
        </div>

        {/* Detailed sheet data */}
        <div className="p-5">
          {details.specificData && Object.keys(details.specificData).length > 0 ? (
            renderObject(details.specificData)
          ) : (
            <div className="text-center py-6 text-gray-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p>No detailed data available for this sheet</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <div className="flex-shrink-0">
            <PatientNav selectedChart={selectedChart} setSelectedChart={setSelectedChart} charts={charts} />
          </div>
          <div className="p-6 overflow-y-auto">
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <div className="flex-shrink-0">
            <PatientNav selectedChart={selectedChart} setSelectedChart={setSelectedChart} charts={charts} />
          </div>
          <div className="p-6 overflow-y-auto">
            <div className="p-4 bg-red-100 text-red-700 rounded-lg">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  const handleViewSheet = (sheet) => {
    setSelectedSheet(sheet);
    setShowModal(true);
  };

  // Function to filter and sort sheets
  const getFilteredSheets = () => {
    const sheetsToFilter = sheets || [];

    return [...sheetsToFilter]
      .filter(sheet => {
        // Filter by search term (check in diagnosis, treatment plan, and type)
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          searchTerm === '' ||
          sheet.type.toLowerCase().includes(searchLower) ||
          (sheet.details?.diagnosis || '').toLowerCase().includes(searchLower) ||
          (sheet.details?.treatmentPlan || '').toLowerCase().includes(searchLower) ||
          (sheet.details?.notes || '').toLowerCase().includes(searchLower);

        // Filter by sheet type
        const matchesType = selectedType === 'All' || sheet.type === selectedType;

        return matchesSearch && matchesType;
      })
      .sort((a, b) => {
        // Sort by date
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();

        return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;
      });
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedSheet(null);
  };

  // Function to handle PDF download for individual sheet cards
  const handleDownloadSheetPDF = async (sheet, event) => {
    event.stopPropagation(); // Prevent opening the modal
    try {
      await generateSheetPDF(sheet, nationalId);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <div className="flex-shrink-0">
          <PatientNav selectedChart={selectedChart} setSelectedChart={setSelectedChart} charts={charts} />
        </div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="p-6 overflow-y-auto"
        >
          <div className="mb-6 flex justify-between items-center">
            <h2 className="text-2xl font-semibold text-gray-800">Sheet History</h2>
            {sheets.length > 0 && (
              <div className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                {getFilteredSheets().length} of {sheets.length} sheets
              </div>
            )}
          </div>

          {/* Search and Filter Controls - Redesigned */}
          <div className="mb-6 flex flex-col md:flex-row gap-3 items-center">
            {/* Search Bar */}
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-[#0077B6]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search by type, diagnosis, treatment plan..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2.5 bg-white border-0 shadow-sm rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#0077B6]"
              />
            </div>

            {/* Type Filter */}
            <div className="flex-shrink-0 w-full md:w-auto">
              <select
                id="type-filter"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="block w-full bg-white border-0 shadow-sm py-2.5 pl-3 pr-10 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0077B6]"
                aria-label="Filter by sheet type"
              >
                {sheetTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Sort Order */}
            <div className="flex-shrink-0 flex items-center bg-white rounded-lg shadow-sm overflow-hidden">
              <button
                onClick={() => setSortOrder('newest')}
                className={`px-4 py-2.5 text-sm font-medium transition-colors ${
                  sortOrder === 'newest'
                    ? 'bg-[#0077B6] text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                aria-label="Sort by newest first"
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                  </svg>
                  Newest
                </div>
              </button>
              <div className="h-6 w-px bg-gray-200"></div>
              <button
                onClick={() => setSortOrder('oldest')}
                className={`px-4 py-2.5 text-sm font-medium transition-colors ${
                  sortOrder === 'oldest'
                    ? 'bg-[#0077B6] text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                aria-label="Sort by oldest first"
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
                  </svg>
                  Oldest
                </div>
              </button>
            </div>
          </div>

          {sheets.length === 0 ? (
            <div className="p-6 bg-white rounded-lg shadow text-center">
              <p className="text-gray-600">No sheets have been saved for this patient yet.</p>
            </div>
          ) : getFilteredSheets().length === 0 ? (
            <div className="p-6 bg-white rounded-lg shadow text-center">
              <p className="text-gray-600">No sheets match your search criteria. Try adjusting your filters.</p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedType('All');
                }}
                className="mt-4 px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#0077B6]/80 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {getFilteredSheets().map((sheet) => (
                <motion.div
                  key={sheet._id}
                  whileHover={{ y: -5, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' }}
                  className="bg-white rounded-xl shadow-md overflow-hidden cursor-pointer transition-all duration-300"
                  onClick={() => handleViewSheet(sheet)}
                >
                  <div className={`h-2 ${
                    sheet.type === 'Operative' ? 'bg-[#0077B6]' :
                    sheet.type === 'Fixed Prosthodontics' ? 'bg-[#20B2AA]' :
                    sheet.type === 'Removable Prosthodontics' ? 'bg-[#28A745]' :
                    sheet.type === 'Endodontics' ? 'bg-[#0077B6]' :
                    sheet.type === 'Periodontics' ? 'bg-[#20B2AA]' :
                    sheet.type === 'Oral Surgery' ? 'bg-[#0077B6]' : 'bg-gray-500'
                  }`}></div>
                  <div className="p-5">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-lg font-semibold text-gray-800">{sheet.type}</h3>
                      <span className="bg-[#0077B6]/10 text-[#0077B6] text-xs px-2 py-1 rounded-full">
                        {new Date(sheet.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3 truncate">
                      {sheet.details?.diagnosis || 'No diagnosis provided'}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">
                        {new Date(sheet.createdAt).toLocaleTimeString()}
                      </span>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={(e) => handleDownloadSheetPDF(sheet, e)}
                          className="p-1.5 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:shadow-md transition-all duration-200 flex items-center justify-center"
                          title="Download PDF"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-3.5 w-3.5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </button>
                        <span className="text-xs text-[#0077B6] font-medium">Click to view details</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>

      {/* Sheet Details Modal */}
      {showModal && selectedSheet && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden"
          >
            {/* Modal Header */}
            <div className="sticky top-0 z-10 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white p-5 rounded-t-2xl flex justify-between items-center">
              <div className="flex items-center">
                <div className={`h-10 w-10 rounded-full flex items-center justify-center mr-3 ${
                  selectedSheet.type === 'Operative' ? 'bg-[#0077B6]/80' :
                  selectedSheet.type === 'Fixed Prosthodontics' ? 'bg-[#20B2AA]/80' :
                  selectedSheet.type === 'Removable Prosthodontics' ? 'bg-[#28A745]/80' :
                  selectedSheet.type === 'Endodontics' ? 'bg-[#0077B6]/80' :
                  selectedSheet.type === 'Periodontics' ? 'bg-[#20B2AA]/80' : 'bg-gray-400'
                }`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-2xl font-bold">{selectedSheet.type} Sheet</h2>
                  <div className="flex text-xs text-white/80 mt-1">
                    <span className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Created: {new Date(selectedSheet.createdAt).toLocaleDateString()}
                    </span>
                    {selectedSheet.updatedAt && selectedSheet.updatedAt !== selectedSheet.createdAt && (
                      <span className="flex items-center ml-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Updated: {new Date(selectedSheet.updatedAt).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center">
                {createPDFDownloadButton('sheet-content', `${selectedSheet.type}_Sheet_${new Date(selectedSheet.createdAt).toLocaleDateString().replace(/\//g, '-')}.pdf`)}
                <button
                  onClick={handleCloseModal}
                  className="text-white hover:text-gray-200 bg-[#0077B6]/80 hover:bg-[#0077B6] rounded-full p-2 transition-colors ml-3"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Modal Content - Scrollable Area */}
            <div className="overflow-y-auto" style={{ maxHeight: 'calc(90vh - 140px)' }}>
              {/* Sheet Details */}
              <div className="p-6" id="sheet-content">
                {renderSheetDetails(selectedSheet)}
              </div>
            </div>

            {/* Modal Footer */}
            <div className="sticky bottom-0 bg-white p-4 border-t rounded-b-2xl flex justify-between items-center">
              <button
                onClick={(e) => handleDownloadSheetPDF(selectedSheet, e)}
                className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:shadow-lg transition-all duration-300 font-semibold flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Download PDF
              </button>
              <button
                onClick={handleCloseModal}
                className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-semibold flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Close
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default History;