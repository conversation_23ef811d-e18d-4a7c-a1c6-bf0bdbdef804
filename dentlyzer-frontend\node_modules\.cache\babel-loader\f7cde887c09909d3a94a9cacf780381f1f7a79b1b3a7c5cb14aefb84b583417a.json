{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Sidebar.jsx\";\nimport { Link } from 'react-router-dom';\nimport { FaHome, FaUserAlt, FaCalendarAlt, FaChartLine, FaStar, FaTooth, FaFlask } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  isOpen,\n  setIsOpen\n}) => {\n  const navItems = [{\n    name: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(FaHome, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 32\n    }, this),\n    path: '/student/dashboard'\n  }, {\n    name: 'Patients',\n    icon: /*#__PURE__*/_jsxDEV(FaUserAlt, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 31\n    }, this),\n    path: '/patients'\n  }, {\n    name: 'Calendar',\n    icon: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 31\n    }, this),\n    path: '/calendar'\n  }, {\n    name: 'Analytics',\n    icon: /*#__PURE__*/_jsxDEV(FaChartLine, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 32\n    }, this),\n    path: '/analytics'\n  }, {\n    name: 'Reviews',\n    icon: /*#__PURE__*/_jsxDEV(FaStar, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 30\n    }, this),\n    path: '/reviews'\n  }];\n  const user = JSON.parse(localStorage.getItem('user')) || {\n    name: 'Student',\n    role: 'student'\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden\",\n      onClick: () => setIsOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'} bg-white shadow-lg flex flex-col`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 flex items-center justify-center\",\n        children: isOpen && /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\n            ,\n            alt: \"ODenta Logo\",\n            className: \"h-10 w-auto\" // Adjust size as needed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 px-2 py-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-2\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              className: \"flex items-center p-3 text-[#333333] hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.05)] rounded-lg transition-colors group\",\n              onClick: () => window.innerWidth < 768 && setIsOpen(false),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-[#333333] group-hover:text-[#0077B6]\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this), isOpen && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 30\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-[rgba(0,119,182,0.1)]\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profile\",\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-[#333333]\",\n              children: user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-[#333333] opacity-70\",\n              children: \"Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["Link", "FaHome", "FaUserAlt", "FaCalendarAlt", "FaChartLine", "FaStar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaFlask", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "isOpen", "setIsOpen", "navItems", "name", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "user", "JSON", "parse", "localStorage", "getItem", "role", "children", "onClick", "to", "src", "alt", "map", "item", "window", "innerWidth", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Sidebar.jsx"], "sourcesContent": ["import { Link } from 'react-router-dom';\r\nimport {\r\n  FaHome,\r\n  FaUserAlt,\r\n  FaCalendarAlt,\r\n  FaChartLine,\r\n  FaStar,\r\n  FaTooth,\r\n  FaFlask,\r\n} from 'react-icons/fa';\r\n\r\nconst Sidebar = ({ isOpen, setIsOpen }) => {\r\n  const navItems = [\r\n    { name: 'Dashboard', icon: <FaHome className=\"h-5 w-5\" />, path: '/student/dashboard' },\r\n    { name: 'Patients', icon: <FaUserAlt className=\"h-5 w-5\" />, path: '/patients' },\r\n    { name: 'Calendar', icon: <FaCalendarAlt className=\"h-5 w-5\" />, path: '/calendar' },\r\n    { name: 'Analytics', icon: <FaChartLine className=\"h-5 w-5\" />, path: '/analytics' },\r\n    { name: 'Reviews', icon: <FaStar className=\"h-5 w-5\" />, path: '/reviews' },\r\n  ];\r\n\r\n  const user = JSON.parse(localStorage.getItem('user')) || { name: 'Student', role: 'student' };\r\n\r\n  return (\r\n    <>\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n\r\n      <div\r\n        className={`fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'} bg-white shadow-lg flex flex-col`}\r\n      >\r\n        <div className=\"p-4 flex items-center justify-center\">\r\n          {/* <Link to=\"/\" className=\"flex items-center justify-center\">\r\n              <FaTooth className=\"w-8 h-8 text-[#0077B6] transition-transform duration-300 hover:scale-110\" />\r\n          </Link> */}\r\n          {isOpen && (\r\n          <Link to=\"/\">\r\n                    <img \r\n                      src=\"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\r\n                      alt=\"ODenta Logo\"\r\n                      className=\"h-10 w-auto\" // Adjust size as needed\r\n                    />\r\n            </Link>\r\n          )}\r\n        </div>\r\n\r\n        <nav className=\"flex-1 px-2 py-4 overflow-y-auto\">\r\n          <ul className=\"space-y-2\">\r\n            {navItems.map((item) => (\r\n              <li key={item.name}>\r\n                <Link\r\n                  to={item.path}\r\n                  className=\"flex items-center p-3 text-[#333333] hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.05)] rounded-lg transition-colors group\"\r\n                  onClick={() => window.innerWidth < 768 && setIsOpen(false)}\r\n                >\r\n                  <span className=\"text-[#333333] group-hover:text-[#0077B6]\">\r\n                    {item.icon}\r\n                  </span>\r\n                  {isOpen && <span className=\"ml-3\">{item.name}</span>}\r\n                </Link>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </nav>\r\n\r\n        <div className=\"p-4 border-t border-[rgba(0,119,182,0.1)]\">\r\n          <Link to=\"/profile\" className=\"flex items-center\">\r\n            <div className=\"h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n              </svg>\r\n            </div>\r\n            {isOpen && (\r\n              <div className=\"ml-3\">\r\n                <p className=\"text-sm font-medium text-[#333333]\">{user.name}</p>\r\n                <p className=\"text-xs text-[#333333] opacity-70\">Student</p>\r\n              </div>\r\n            )}\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,kBAAkB;AACvC,SACEC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,WAAW,EACXC,MAAM,EACNC,OAAO,EACPC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAU,CAAC,KAAK;EACzC,MAAMC,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAER,OAAA,CAACR,MAAM;MAACiB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAqB,CAAC,EACvF;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAER,OAAA,CAACP,SAAS;MAACgB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAY,CAAC,EAChF;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAER,OAAA,CAACN,aAAa;MAACe,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAY,CAAC,EACpF;IAAEP,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAER,OAAA,CAACL,WAAW;MAACc,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAa,CAAC,EACpF;IAAEP,IAAI,EAAE,SAAS;IAAEC,IAAI,eAAER,OAAA,CAACJ,MAAM;MAACa,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAW,CAAC,CAC5E;EAED,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI;IAAEZ,IAAI,EAAE,SAAS;IAAEa,IAAI,EAAE;EAAU,CAAC;EAE7F,oBACEpB,OAAA,CAAAE,SAAA;IAAAmB,QAAA,GACGjB,MAAM,iBACLJ,OAAA;MACES,SAAS,EAAC,qDAAqD;MAC/Da,OAAO,EAAEA,CAAA,KAAMjB,SAAS,CAAC,KAAK;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF,eAEDb,OAAA;MACES,SAAS,EAAE,yEAAyEL,MAAM,GAAG,oBAAoB,GAAG,yCAAyC,mCAAoC;MAAAiB,QAAA,gBAEjMrB,OAAA;QAAKS,SAAS,EAAC,sCAAsC;QAAAY,QAAA,EAIlDjB,MAAM,iBACPJ,OAAA,CAACT,IAAI;UAACgC,EAAE,EAAC,GAAG;UAAAF,QAAA,eACFrB,OAAA;YACEwB,GAAG,EAAC,wBAAwB,CAAC;YAAA;YAC7BC,GAAG,EAAC,aAAa;YACjBhB,SAAS,EAAC,aAAa,CAAC;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENb,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAY,QAAA,eAC/CrB,OAAA;UAAIS,SAAS,EAAC,WAAW;UAAAY,QAAA,EACtBf,QAAQ,CAACoB,GAAG,CAAEC,IAAI,iBACjB3B,OAAA;YAAAqB,QAAA,eACErB,OAAA,CAACT,IAAI;cACHgC,EAAE,EAAEI,IAAI,CAACb,IAAK;cACdL,SAAS,EAAC,8HAA8H;cACxIa,OAAO,EAAEA,CAAA,KAAMM,MAAM,CAACC,UAAU,GAAG,GAAG,IAAIxB,SAAS,CAAC,KAAK,CAAE;cAAAgB,QAAA,gBAE3DrB,OAAA;gBAAMS,SAAS,EAAC,2CAA2C;gBAAAY,QAAA,EACxDM,IAAI,CAACnB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACNT,MAAM,iBAAIJ,OAAA;gBAAMS,SAAS,EAAC,MAAM;gBAAAY,QAAA,EAAEM,IAAI,CAACpB;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC,GAVAc,IAAI,CAACpB,IAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENb,OAAA;QAAKS,SAAS,EAAC,2CAA2C;QAAAY,QAAA,eACxDrB,OAAA,CAACT,IAAI;UAACgC,EAAE,EAAC,UAAU;UAACd,SAAS,EAAC,mBAAmB;UAAAY,QAAA,gBAC/CrB,OAAA;YAAKS,SAAS,EAAC,iGAAiG;YAAAY,QAAA,eAC9GrB,OAAA;cAAK8B,KAAK,EAAC,4BAA4B;cAACrB,SAAS,EAAC,SAAS;cAACsB,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eAC/GrB,OAAA;gBAAMkC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAmI;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLT,MAAM,iBACLJ,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAY,QAAA,gBACnBrB,OAAA;cAAGS,SAAS,EAAC,oCAAoC;cAAAY,QAAA,EAAEN,IAAI,CAACR;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEb,OAAA;cAAGS,SAAS,EAAC,mCAAmC;cAAAY,QAAA,EAAC;YAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACyB,EAAA,GA3EInC,OAAO;AA6Eb,eAAeA,OAAO;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}