import { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaSignature, FaFileAlt, FaCheck, FaTimes, FaPen, FaFont, FaUpload } from 'react-icons/fa';
import axios from 'axios';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';
import { useAuth } from '../context/AuthContext';
import Loader from '../components/Loader';

const Consent = () => {
  const { nationalId } = useParams();
  const { user, token } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [signatureMethod, setSignatureMethod] = useState('text'); // 'text', 'draw', or 'upload'
  const [signatureText, setSignatureText] = useState('');
  const [signatureImage, setSignatureImage] = useState('');
  const [uploadedSignature, setUploadedSignature] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Canvas refs and state
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [canvasContext, setCanvasContext] = useState(null);

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/public/${nationalId}`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        // Ensure the patient data has a consent field, even if it's empty
        const patientWithConsent = {
          ...response.data,
          consent: response.data.consent || { isSigned: false }
        };

        setPatientData(patientWithConsent);

        // If patient already has a signed consent, populate the signature fields
        if (patientWithConsent.consent && patientWithConsent.consent.isSigned) {
          if (patientWithConsent.consent.signatureText) {
            setSignatureText(patientWithConsent.consent.signatureText);
            setSignatureMethod('text');
          } else if (patientWithConsent.consent.signatureImage) {
            // Check if it's a drawn signature or uploaded image
            if (patientWithConsent.consent.signatureImage.startsWith('data:image')) {
              setSignatureImage(patientWithConsent.consent.signatureImage);
              setSignatureMethod('draw');
            } else {
              setUploadedSignature(patientWithConsent.consent.signatureImage);
              setSignatureMethod('upload');
            }
          }
        }
      } catch (err) {
        console.error('Error fetching patient data:', err.response?.data || err.message);
        setError(err.response?.data?.message || 'Failed to load patient data');
      } finally {
        setLoading(false);
      }
    };

    if (token && nationalId) {
      fetchPatientData();
    }
  }, [nationalId, token]);

  // Initialize canvas when component mounts
  useEffect(() => {
    // This effect needs to run after the canvas is rendered in the DOM
    const initializeCanvas = () => {
      if (canvasRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // Set canvas properties
          ctx.lineWidth = 2;
          ctx.lineCap = 'round';
          ctx.lineJoin = 'round';
          ctx.strokeStyle = 'black';

          // Clear the canvas first
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Set canvas background to white for better visibility
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          setCanvasContext(ctx);

          // If there's a signature image and we're in draw mode, draw it on the canvas
          if (signatureImage && signatureMethod === 'draw') {
            const img = new Image();
            img.onload = () => {
              // Clear canvas before drawing
              ctx.clearRect(0, 0, canvas.width, canvas.height);
              ctx.fillStyle = 'white';
              ctx.fillRect(0, 0, canvas.width, canvas.height);

              // Draw the image centered and scaled appropriately
              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            };
            img.src = signatureImage;
          }
        }
      }
    };

    // Initialize immediately and also after a short delay to ensure DOM is ready
    initializeCanvas();
    const timer = setTimeout(initializeCanvas, 100);

    // Also reinitialize when the window is resized
    const handleResize = () => {
      initializeCanvas();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, [signatureMethod, signatureImage]);

  // Drawing functions
  const getMousePos = (canvas, evt) => {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    return {
      x: (evt.clientX - rect.left) * scaleX,
      y: (evt.clientY - rect.top) * scaleY
    };
  };

  const startDrawing = (e) => {
    if (patientData?.consent?.isSigned || !canvasRef.current) return; // Prevent drawing if already signed or canvas not ready

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const pos = getMousePos(canvas, e);

    // Set drawing style
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.strokeStyle = 'black';

    ctx.beginPath();
    ctx.moveTo(pos.x, pos.y);
    setCanvasContext(ctx);
    setIsDrawing(true);
  };

  const draw = (e) => {
    if (!isDrawing || patientData?.consent?.isSigned || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const pos = getMousePos(canvas, e);

    ctx.lineTo(pos.x, pos.y);
    ctx.stroke();
  };

  const stopDrawing = () => {
    if (isDrawing && canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      if (ctx) {
        ctx.closePath();
      }
      setIsDrawing(false);

      // Save the canvas content as an image
      const dataUrl = canvasRef.current.toDataURL('image/png');
      setSignatureImage(dataUrl);
    }
  };

  const clearCanvas = () => {
    if (patientData?.consent?.isSigned || !canvasRef.current) return; // Prevent clearing if already signed

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Reset to white background
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Reset drawing state
    setIsDrawing(false);
    setSignatureImage('');

    // Reset context properties
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.strokeStyle = 'black';
    setCanvasContext(ctx);
  };

  const handleSignatureMethodChange = (method) => {
    if (patientData?.consent?.isSigned) return; // Prevent changing if already signed
    setSignatureMethod(method);
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file.');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB.');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setUploadedSignature(e.target.result);
      setError(''); // Clear any previous errors
    };
    reader.readAsDataURL(file);
  };

  const handleSignConsent = async () => {
    if (patientData?.consent?.isSigned) {
      setError('This consent form has already been signed.');
      return;
    }

    // Validate signature
    if (signatureMethod === 'text' && !signatureText.trim()) {
      setError('Please enter your signature in the text field.');
      return;
    }

    if (signatureMethod === 'draw' && !signatureImage) {
      setError('Please draw your signature on the canvas.');
      return;
    }

    if (signatureMethod === 'upload' && !uploadedSignature) {
      setError('Please upload a signature image.');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/consent`,
        {
          signatureText: signatureMethod === 'text' ? signatureText : '',
          signatureImage: signatureMethod === 'draw' ? signatureImage : signatureMethod === 'upload' ? uploadedSignature : ''
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      // Update the patient data with the new consent information
      setPatientData({
        ...patientData,
        consent: response.data.consent
      });

      setSuccess('Consent form signed successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('Error signing consent:', err.response?.data || err.message);
      setError(err.response?.data?.message || 'Failed to sign consent form');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <PatientNav />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <Loader />
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-4xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-6 p-4 bg-red-100 text-[#333333] rounded-lg"
              >
                {error}
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-6 p-4 bg-[#28A745]/20 text-[#28A745] rounded-lg"
              >
                {success}
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-xl shadow-lg overflow-hidden"
            >
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-2xl font-bold text-[#0077B6]">
                      Dental Treatment Consent Form
                    </h1>
                    <p className="text-gray-600 mt-1">
                      {patientData?.consent?.isSigned
                        ? 'This consent form has been signed and cannot be modified.'
                        : 'Please read carefully and sign to provide your consent for dental treatment.'}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${patientData?.consent?.isSigned ? 'bg-[#28A745]/20' : 'bg-[#0077B6]/10'}`}>
                    {patientData?.consent?.isSigned ? (
                      <FaCheck className="h-6 w-6 text-[#28A745]" />
                    ) : (
                      <FaSignature className="h-6 w-6 text-[#0077B6]" />
                    )}
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="prose max-w-none">
                  <h3 className="text-lg font-semibold mb-4">General Dental Treatment Consent</h3>

                  <p className="mb-4">
                    I, {patientData?.fullName || '[Patient Name]'}, hereby authorize the dental team to perform the following dental treatment or oral surgery procedure(s):
                    examination, radiographs, prophylaxis (cleaning), fluoride treatment, restorations (fillings), crowns, bridges, extractions, root canal therapy, periodontal therapy, and other procedures deemed necessary.
                  </p>

                  <p className="mb-4">
                    I understand that there are risks associated with dental treatment including but not limited to:
                  </p>

                  <ul className="list-disc pl-5 mb-4">
                    <li>Sensitivity or pain in treated or adjacent teeth</li>
                    <li>Infection requiring additional treatment</li>
                    <li>Fracture of tooth/teeth or dental restorations</li>
                    <li>Swelling, bleeding, or discomfort following procedures</li>
                    <li>Reactions to medications, anesthetics, or materials used</li>
                    <li>Changes in occlusion (bite) requiring adjustment</li>
                  </ul>

                  <p className="mb-4">
                    I understand that during treatment, unforeseen conditions may arise which may necessitate procedures different from those planned. I consent to those additional procedures that are necessary in the professional judgment of my treating dentist.
                  </p>

                  <p className="mb-4">
                    I understand that I may ask questions about the planned procedures and that I have the right to be informed of alternative treatments. I have disclosed my complete medical history, including medications and allergies.
                  </p>

                  <p className="mb-4">
                    I consent to the administration of local anesthesia, antibiotics, analgesics, or any other medication necessary for dental treatment. I understand the risks involved in the administration of these medications.
                  </p>

                  <p className="mb-4">
                    I consent to the making of photographs, video recordings, and x-rays before, during, and after treatment, and to their use for scientific, educational, or research purposes.
                  </p>

                  <p className="mb-6">
                    By signing below, I acknowledge that I have read and understand this consent form, had the opportunity to ask questions, and give my consent for dental treatment.
                  </p>

                  {/* Signature Section */}
                  <div className="border-t border-gray-200 pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold">Patient Signature</h4>
                      {patientData?.consent?.isSigned && (
                        <div className="text-sm text-green-600 font-medium flex items-center">
                          <FaCheck className="mr-1" /> Signed on {new Date(patientData.consent.signedAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>

                    {!patientData?.consent?.isSigned && (
                      <div className="mb-6">
                        <div className="flex flex-wrap gap-4 mb-4">
                          <button
                            onClick={() => handleSignatureMethodChange('text')}
                            className={`flex items-center px-4 py-2 rounded-lg ${
                              signatureMethod === 'text'
                                ? 'bg-[#0077B6]/10 text-[#0077B6] border border-[#0077B6]/30'
                                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                            }`}
                          >
                            <FaFont className="mr-2" /> Text Signature
                          </button>
                          <button
                            onClick={() => handleSignatureMethodChange('draw')}
                            className={`flex items-center px-4 py-2 rounded-lg ${
                              signatureMethod === 'draw'
                                ? 'bg-[#20B2AA]/10 text-[#20B2AA] border border-[#20B2AA]/30'
                                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                            }`}
                          >
                            <FaPen className="mr-2" /> Draw Signature
                          </button>
                          <button
                            onClick={() => handleSignatureMethodChange('upload')}
                            className={`flex items-center px-4 py-2 rounded-lg ${
                              signatureMethod === 'upload'
                                ? 'bg-[#28A745]/10 text-[#28A745] border border-[#28A745]/30'
                                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                            }`}
                          >
                            <FaUpload className="mr-2" /> Upload Signature
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Text Signature */}
                    {signatureMethod === 'text' && (
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Type your full name as your signature:
                        </label>
                        <input
                          type="text"
                          value={signatureText}
                          onChange={(e) => setSignatureText(e.target.value)}
                          disabled={patientData?.consent?.isSigned}
                          placeholder="Type your full name here"
                          className={`w-full px-4 py-2 border ${
                            patientData?.consent?.isSigned
                              ? 'bg-gray-100 border-gray-300'
                              : 'border-gray-300 focus:border-[#0077B6] focus:ring-1 focus:ring-[#0077B6]'
                          } rounded-lg`}
                        />
                      </div>
                    )}

                    {/* Drawing Signature */}
                    {signatureMethod === 'draw' && (
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Draw your signature below:
                        </label>
                        <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
                          <canvas
                            ref={canvasRef}
                            width={600}
                            height={150}
                            onMouseDown={startDrawing}
                            onMouseMove={draw}
                            onMouseUp={stopDrawing}
                            onMouseLeave={stopDrawing}
                            onTouchStart={(e) => {
                              e.preventDefault(); // Prevent scrolling when drawing
                              if (e.touches && e.touches.length > 0) {
                                const touch = e.touches[0];
                                startDrawing({
                                  clientX: touch.clientX,
                                  clientY: touch.clientY
                                });
                              }
                            }}
                            onTouchMove={(e) => {
                              e.preventDefault(); // Prevent scrolling when drawing
                              if (e.touches && e.touches.length > 0) {
                                const touch = e.touches[0];
                                draw({
                                  clientX: touch.clientX,
                                  clientY: touch.clientY
                                });
                              }
                            }}
                            onTouchEnd={stopDrawing}
                            className={`w-full ${patientData?.consent?.isSigned ? 'cursor-not-allowed' : 'cursor-crosshair'}`}
                          />
                        </div>
                        {!patientData?.consent?.isSigned && (
                          <button
                            onClick={clearCanvas}
                            className="mt-2 px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                          >
                            Clear
                          </button>
                        )}
                      </div>
                    )}

                    {/* Upload Signature */}
                    {signatureMethod === 'upload' && (
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Upload your signature image:
                        </label>
                        <div className="border border-gray-300 rounded-lg p-4 bg-white">
                          <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleFileUpload}
                            accept="image/*"
                            disabled={patientData?.consent?.isSigned}
                            className="hidden"
                          />
                          <div className="flex flex-col items-center">
                            {uploadedSignature ? (
                              <div className="mb-4">
                                <img
                                  src={uploadedSignature}
                                  alt="Uploaded signature"
                                  className="max-h-32 max-w-full border border-gray-200 rounded"
                                />
                              </div>
                            ) : (
                              <div className="mb-4 p-8 border-2 border-dashed border-gray-300 rounded-lg text-center">
                                <FaUpload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                                <p className="text-sm text-gray-600">No signature uploaded</p>
                              </div>
                            )}
                            {!patientData?.consent?.isSigned && (
                              <button
                                onClick={() => fileInputRef.current?.click()}
                                className="px-4 py-2 bg-[#28A745] text-white rounded-lg hover:bg-[#28A745]/90 transition-colors flex items-center"
                              >
                                <FaUpload className="mr-2" />
                                {uploadedSignature ? 'Change Image' : 'Select Image'}
                              </button>
                            )}
                          </div>
                        </div>
                        <p className="mt-2 text-xs text-gray-500">
                          Supported formats: JPG, PNG, GIF. Maximum file size: 5MB.
                        </p>
                      </div>
                    )}

                    {/* Display signed signature */}
                    {patientData?.consent?.isSigned && (
                      <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <h4 className="text-sm font-medium text-green-800 mb-2">Signed Signature:</h4>
                        <div className="bg-white p-3 rounded border">
                          {patientData.consent.signatureText ? (
                            <p className="font-signature text-lg text-gray-900">
                              {patientData.consent.signatureText}
                            </p>
                          ) : patientData.consent.signatureImage ? (
                            <img
                              src={patientData.consent.signatureImage}
                              alt="Patient signature"
                              className="max-h-20 max-w-full"
                            />
                          ) : (
                            <p className="text-gray-500 text-sm">No signature available</p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Patient Info and Date */}
                    <div className="flex flex-col md:flex-row justify-between mt-6">
                      <div className="mb-4 md:mb-0">
                        <p className="text-sm font-medium text-gray-700">Patient Name:</p>
                        <p className="text-lg font-semibold text-gray-900">
                          {patientData?.fullName || 'Patient Name'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Date:</p>
                        <p className="text-lg font-semibold text-gray-900">
                          {patientData?.consent?.signedAt
                            ? new Date(patientData.consent.signedAt).toLocaleDateString()
                            : new Date().toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer with action buttons */}
              <div className="p-6 border-t border-gray-100 bg-gray-50">
                <div className="flex justify-end">
                  {!patientData?.consent?.isSigned && (
                    <button
                      onClick={handleSignConsent}
                      disabled={isSubmitting}
                      className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Processing...
                        </>
                      ) : (
                        <>
                          <FaSignature className="h-4 w-4" />
                          Sign Consent Form
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Consent;
