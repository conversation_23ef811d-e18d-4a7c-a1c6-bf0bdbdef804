{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\VideoCall.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport Webcam from 'react-webcam';\nimport { toast } from 'react-toastify';\nimport { motion } from 'framer-motion';\nimport { FaPlay, FaStop, FaCamera, FaImage, FaVideo, FaCog } from 'react-icons/fa';\nimport './VideoCall.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCall = ({\n  onConnectionStatus,\n  onStartAnalysis,\n  onImageCaptured,\n  onDetectionResults\n}) => {\n  _s();\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [capturedImages, setCapturedImages] = useState([]);\n  const [autoCapture, setAutoCapture] = useState(true);\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\n  const [availableCameras, setAvailableCameras] = useState([]);\n  const [selectedCamera, setSelectedCamera] = useState(null);\n  const [showCameraSettings, setShowCameraSettings] = useState(false);\n  const webcamRef = useRef(null);\n  const intervalRef = useRef(null);\n\n  // Get available cameras\n  const getAvailableCameras = useCallback(async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const videoDevices = devices.filter(device => device.kind === 'videoinput');\n      console.log('Available cameras:', videoDevices);\n      setAvailableCameras(videoDevices);\n\n      // Auto-select first USB camera or fallback to first available\n      const usbCamera = videoDevices.find(device => device.label.toLowerCase().includes('usb') || device.label.toLowerCase().includes('external') || device.label.toLowerCase().includes('camera'));\n      if (usbCamera) {\n        setSelectedCamera(usbCamera.deviceId);\n        console.log('Selected USB camera:', usbCamera.label);\n      } else if (videoDevices.length > 0) {\n        setSelectedCamera(videoDevices[0].deviceId);\n        console.log('Selected first available camera:', videoDevices[0].label);\n      }\n    } catch (error) {\n      console.error('Error getting cameras:', error);\n      toast.error('Failed to get camera list');\n    }\n  }, []);\n  const videoConstraints = {\n    width: 640,\n    height: 480,\n    deviceId: selectedCamera ? {\n      exact: selectedCamera\n    } : undefined\n  };\n  const sendImageForAnalysis = useCallback(async imageSrc => {\n    try {\n      onStartAnalysis();\n\n      // Convert base64 to blob\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('image', blob, 'capture.jpg');\n\n      // Send to backend for YOLOv8 analysis\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error('Analysis failed');\n      }\n      const results = await response.json();\n      console.log('YOLOv8 Results:', results);\n      if (onDetectionResults && results && results.results) {\n        onDetectionResults(results.results);\n      }\n    } catch (error) {\n      console.error('Error sending image for analysis:', error);\n      toast.error('Failed to analyze image');\n      if (onDetectionResults) {\n        onDetectionResults([]);\n      }\n    }\n  }, [onStartAnalysis, onDetectionResults]);\n  const captureImage = useCallback(() => {\n    if (webcamRef.current && isStreaming) {\n      setIsCapturing(true);\n      const imageSrc = webcamRef.current.getScreenshot();\n      if (imageSrc) {\n        const newImage = {\n          id: Date.now(),\n          src: imageSrc,\n          timestamp: new Date().toISOString(),\n          analyzed: false\n        };\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\n\n        // Pass the captured image to parent component for YOLOv8 analysis\n        if (onImageCaptured) {\n          onImageCaptured(imageSrc);\n        }\n\n        // Send to YOLOv8 analysis\n        sendImageForAnalysis(imageSrc);\n        toast.success('Image captured and sent for analysis');\n      }\n      setIsCapturing(false);\n    }\n  }, [isStreaming, onImageCaptured, sendImageForAnalysis]);\n  const startAutoCapture = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n    }\n    intervalRef.current = setInterval(() => {\n      if (isStreaming && webcamRef.current) {\n        captureImage();\n      }\n    }, captureInterval);\n  }, [isStreaming, captureInterval, captureImage]);\n  const startStream = useCallback(() => {\n    if (!selectedCamera) {\n      toast.error('Please select a camera first');\n      return;\n    }\n    setIsStreaming(true);\n    onConnectionStatus(true);\n    toast.success('Video stream started');\n\n    // Start automatic capture if enabled\n    if (autoCapture) {\n      startAutoCapture();\n    }\n  }, [autoCapture, onConnectionStatus, startAutoCapture, selectedCamera]);\n  const stopStream = useCallback(() => {\n    setIsStreaming(false);\n    onConnectionStatus(false);\n    stopAutoCapture();\n    toast.info('Video stream stopped');\n  }, [onConnectionStatus]);\n  const stopAutoCapture = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  };\n  const toggleAutoCapture = () => {\n    setAutoCapture(!autoCapture);\n    if (!autoCapture) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  };\n  const handleCameraChange = deviceId => {\n    setSelectedCamera(deviceId);\n    if (isStreaming) {\n      stopStream();\n      setTimeout(() => {\n        startStream();\n      }, 500);\n    }\n  };\n  useEffect(() => {\n    getAvailableCameras();\n  }, [getAvailableCameras]);\n  useEffect(() => {\n    return () => {\n      stopAutoCapture();\n    };\n  }, []);\n  useEffect(() => {\n    if (autoCapture && isStreaming) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  }, [autoCapture, captureInterval, isStreaming, startAutoCapture]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 p-4 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: \"Camera Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCameraSettings(!showCameraSettings),\n          className: \"flex items-center text-sm text-[#0077B6] hover:text-[#20B2AA]\",\n          children: [/*#__PURE__*/_jsxDEV(FaCog, {\n            className: \"mr-1 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), showCameraSettings ? 'Hide' : 'Show', \" Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), showCameraSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Select Camera:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedCamera || '',\n          onChange: e => handleCameraChange(e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select a camera...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), availableCameras.map(camera => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: camera.deviceId,\n            children: camera.label || `Camera ${camera.deviceId.slice(0, 8)}...`\n          }, camera.deviceId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500\",\n          children: [availableCameras.length, \" camera(s) detected\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-3 items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: `flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${isStreaming ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg' : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'}`,\n          onClick: isStreaming ? stopStream : startStream,\n          disabled: !selectedCamera,\n          children: [isStreaming ? /*#__PURE__*/_jsxDEV(FaStop, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 28\n          }, this) : /*#__PURE__*/_jsxDEV(FaPlay, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 66\n          }, this), isStreaming ? 'Stop Stream' : 'Start Stream']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n          onClick: captureImage,\n          disabled: !isStreaming || isCapturing,\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), isCapturing ? 'Capturing...' : 'Capture Image']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center text-sm font-medium text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: autoCapture,\n            onChange: toggleAutoCapture,\n            disabled: !isStreaming,\n            className: \"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), \"Auto Capture\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), autoCapture && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: captureInterval,\n          onChange: e => setCaptureInterval(Number(e.target.value)),\n          disabled: !isStreaming,\n          className: \"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 3000,\n            children: \"Every 3s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5000,\n            children: \"Every 5s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10000,\n            children: \"Every 10s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\",\n      children: [isStreaming ? /*#__PURE__*/_jsxDEV(Webcam, {\n        ref: webcamRef,\n        audio: false,\n        screenshotFormat: \"image/jpeg\",\n        videoConstraints: videoConstraints,\n        className: \"w-full h-80 md:h-96 lg:h-[500px] object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-80 md:h-96 lg:h-[500px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-white\",\n          children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n            className: \"h-16 w-16 mx-auto mb-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-medium\",\n            children: \"Click \\\"Start Stream\\\" to begin video consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400 mt-2\",\n            children: \"Live dental examination and analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this), isCapturing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-5 w-5 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), \"Capturing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: [\"Recent Captures (\", capturedImages.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), capturedImages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n        children: capturedImages.map(image => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: image.src,\n            alt: \"Captured\",\n            className: \"w-full h-24 object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 mb-1\",\n              children: new Date(image.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.analyzed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n              children: image.analyzed ? '✓ Analyzed' : '⏳ Pending'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n          className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No captures yet. Start streaming and capture images for analysis.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCall, \"nrhhfyHGgbyk4c3Y3JXbygjVbho=\");\n_c = VideoCall;\nexport default VideoCall;\nvar _c;\n$RefreshReg$(_c, \"VideoCall\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Webcam", "toast", "motion", "FaPlay", "FaStop", "FaCamera", "FaImage", "FaVideo", "FaCog", "jsxDEV", "_jsxDEV", "VideoCall", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "onDetectionResults", "_s", "isStreaming", "setIsStreaming", "isCapturing", "setIsCapturing", "capturedImages", "setCapturedImages", "autoCapture", "setAutoCapture", "captureInterval", "setCaptureInterval", "availableCameras", "setAvailableCameras", "selectedCamera", "setSelectedCamera", "showCameraSettings", "setShowCameraSettings", "webcamRef", "intervalRef", "getAvailableCameras", "devices", "navigator", "mediaDevices", "enumerateDevices", "videoDevices", "filter", "device", "kind", "console", "log", "usbCamera", "find", "label", "toLowerCase", "includes", "deviceId", "length", "error", "videoConstraints", "width", "height", "exact", "undefined", "sendImageForAnalysis", "imageSrc", "base64Data", "replace", "blob", "fetch", "then", "res", "formData", "FormData", "append", "response", "method", "body", "ok", "Error", "results", "json", "captureImage", "current", "getScreenshot", "newImage", "id", "Date", "now", "src", "timestamp", "toISOString", "analyzed", "prev", "slice", "success", "startAutoCapture", "clearInterval", "setInterval", "startStream", "stopStream", "stopAutoCapture", "info", "toggleAutoCapture", "handleCameraChange", "setTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "e", "target", "map", "camera", "button", "whileHover", "scale", "whileTap", "disabled", "type", "checked", "Number", "ref", "audio", "screenshotFormat", "image", "div", "alt", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/VideoCall.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport Webcam from 'react-webcam';\r\nimport { toast } from 'react-toastify';\r\nimport { motion } from 'framer-motion';\r\nimport { FaPlay, FaStop, FaCamera, FaImage, FaVideo, FaCog } from 'react-icons/fa';\r\nimport './VideoCall.css';\r\n\r\nconst VideoCall = ({ onConnectionStatus, onStartAnalysis, onImageCaptured, onDetectionResults }) => {\r\n  const [isStreaming, setIsStreaming] = useState(false);\r\n  const [isCapturing, setIsCapturing] = useState(false);\r\n  const [capturedImages, setCapturedImages] = useState([]);\r\n  const [autoCapture, setAutoCapture] = useState(true);\r\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\r\n  const [availableCameras, setAvailableCameras] = useState([]);\r\n  const [selectedCamera, setSelectedCamera] = useState(null);\r\n  const [showCameraSettings, setShowCameraSettings] = useState(false);\r\n  const webcamRef = useRef(null);\r\n  const intervalRef = useRef(null);\r\n\r\n  // Get available cameras\r\n  const getAvailableCameras = useCallback(async () => {\r\n    try {\r\n      const devices = await navigator.mediaDevices.enumerateDevices();\r\n      const videoDevices = devices.filter(device => device.kind === 'videoinput');\r\n      \r\n      console.log('Available cameras:', videoDevices);\r\n      setAvailableCameras(videoDevices);\r\n      \r\n      // Auto-select first USB camera or fallback to first available\r\n      const usbCamera = videoDevices.find(device => \r\n        device.label.toLowerCase().includes('usb') || \r\n        device.label.toLowerCase().includes('external') ||\r\n        device.label.toLowerCase().includes('camera')\r\n      );\r\n      \r\n      if (usbCamera) {\r\n        setSelectedCamera(usbCamera.deviceId);\r\n        console.log('Selected USB camera:', usbCamera.label);\r\n      } else if (videoDevices.length > 0) {\r\n        setSelectedCamera(videoDevices[0].deviceId);\r\n        console.log('Selected first available camera:', videoDevices[0].label);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error getting cameras:', error);\r\n      toast.error('Failed to get camera list');\r\n    }\r\n  }, []);\r\n\r\n  const videoConstraints = {\r\n    width: 640,\r\n    height: 480,\r\n    deviceId: selectedCamera ? { exact: selectedCamera } : undefined\r\n  };\r\n\r\n  const sendImageForAnalysis = useCallback(async (imageSrc) => {\r\n    try {\r\n      onStartAnalysis();\r\n\r\n      // Convert base64 to blob\r\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\r\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\r\n\r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('image', blob, 'capture.jpg');\r\n\r\n      // Send to backend for YOLOv8 analysis\r\n      const response = await fetch('/api/analyze', {\r\n        method: 'POST',\r\n        body: formData\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Analysis failed');\r\n      }\r\n\r\n      const results = await response.json();\r\n      console.log('YOLOv8 Results:', results);\r\n      if (onDetectionResults && results && results.results) {\r\n        onDetectionResults(results.results);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Error sending image for analysis:', error);\r\n      toast.error('Failed to analyze image');\r\n      if (onDetectionResults) {\r\n        onDetectionResults([]);\r\n      }\r\n    }\r\n  }, [onStartAnalysis, onDetectionResults]);\r\n\r\n  const captureImage = useCallback(() => {\r\n    if (webcamRef.current && isStreaming) {\r\n      setIsCapturing(true);\r\n      const imageSrc = webcamRef.current.getScreenshot();\r\n\r\n      if (imageSrc) {\r\n        const newImage = {\r\n          id: Date.now(),\r\n          src: imageSrc,\r\n          timestamp: new Date().toISOString(),\r\n          analyzed: false\r\n        };\r\n\r\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\r\n\r\n        // Pass the captured image to parent component for YOLOv8 analysis\r\n        if (onImageCaptured) {\r\n          onImageCaptured(imageSrc);\r\n        }\r\n\r\n        // Send to YOLOv8 analysis\r\n        sendImageForAnalysis(imageSrc);\r\n\r\n        toast.success('Image captured and sent for analysis');\r\n      }\r\n\r\n      setIsCapturing(false);\r\n    }\r\n  }, [isStreaming, onImageCaptured, sendImageForAnalysis]);\r\n\r\n  const startAutoCapture = useCallback(() => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n    }\r\n\r\n    intervalRef.current = setInterval(() => {\r\n      if (isStreaming && webcamRef.current) {\r\n        captureImage();\r\n      }\r\n    }, captureInterval);\r\n  }, [isStreaming, captureInterval, captureImage]);\r\n\r\n  const startStream = useCallback(() => {\r\n    if (!selectedCamera) {\r\n      toast.error('Please select a camera first');\r\n      return;\r\n    }\r\n    \r\n    setIsStreaming(true);\r\n    onConnectionStatus(true);\r\n    toast.success('Video stream started');\r\n\r\n    // Start automatic capture if enabled\r\n    if (autoCapture) {\r\n      startAutoCapture();\r\n    }\r\n  }, [autoCapture, onConnectionStatus, startAutoCapture, selectedCamera]);\r\n\r\n  const stopStream = useCallback(() => {\r\n    setIsStreaming(false);\r\n    onConnectionStatus(false);\r\n    stopAutoCapture();\r\n    toast.info('Video stream stopped');\r\n  }, [onConnectionStatus]);\r\n\r\n  const stopAutoCapture = () => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n      intervalRef.current = null;\r\n    }\r\n  };\r\n\r\n  const toggleAutoCapture = () => {\r\n    setAutoCapture(!autoCapture);\r\n    if (!autoCapture) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  };\r\n\r\n  const handleCameraChange = (deviceId) => {\r\n    setSelectedCamera(deviceId);\r\n    if (isStreaming) {\r\n      stopStream();\r\n      setTimeout(() => {\r\n        startStream();\r\n      }, 500);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getAvailableCameras();\r\n  }, [getAvailableCameras]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      stopAutoCapture();\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (autoCapture && isStreaming) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  }, [autoCapture, captureInterval, isStreaming, startAutoCapture]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Camera Selection */}\r\n      <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n        <div className=\"flex items-center justify-between mb-3\">\r\n          <h3 className=\"text-sm font-medium text-gray-700\">Camera Settings</h3>\r\n          <button\r\n            onClick={() => setShowCameraSettings(!showCameraSettings)}\r\n            className=\"flex items-center text-sm text-[#0077B6] hover:text-[#20B2AA]\"\r\n          >\r\n            <FaCog className=\"mr-1 h-4 w-4\" />\r\n            {showCameraSettings ? 'Hide' : 'Show'} Settings\r\n          </button>\r\n        </div>\r\n        \r\n        {showCameraSettings && (\r\n          <div className=\"space-y-3\">\r\n            <label className=\"block text-sm font-medium text-gray-700\">\r\n              Select Camera:\r\n            </label>\r\n            <select\r\n              value={selectedCamera || ''}\r\n              onChange={(e) => handleCameraChange(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\"\r\n            >\r\n              <option value=\"\">Select a camera...</option>\r\n              {availableCameras.map((camera) => (\r\n                <option key={camera.deviceId} value={camera.deviceId}>\r\n                  {camera.label || `Camera ${camera.deviceId.slice(0, 8)}...`}\r\n                </option>\r\n              ))}\r\n            </select>\r\n            <p className=\"text-xs text-gray-500\">\r\n              {availableCameras.length} camera(s) detected\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Video Controls */}\r\n      <div className=\"flex flex-wrap gap-3 items-center justify-between\">\r\n        <div className=\"flex gap-3\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className={`flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${\r\n              isStreaming\r\n                ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg'\r\n                : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'\r\n            }`}\r\n            onClick={isStreaming ? stopStream : startStream}\r\n            disabled={!selectedCamera}\r\n          >\r\n            {isStreaming ? <FaStop className=\"mr-2 h-4 w-4\" /> : <FaPlay className=\"mr-2 h-4 w-4\" />}\r\n            {isStreaming ? 'Stop Stream' : 'Start Stream'}\r\n          </motion.button>\r\n\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            onClick={captureImage}\r\n            disabled={!isStreaming || isCapturing}\r\n          >\r\n            <FaCamera className=\"mr-2 h-4 w-4\" />\r\n            {isCapturing ? 'Capturing...' : 'Capture Image'}\r\n          </motion.button>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-4\">\r\n          <label className=\"flex items-center text-sm font-medium text-gray-700\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={autoCapture}\r\n              onChange={toggleAutoCapture}\r\n              disabled={!isStreaming}\r\n              className=\"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\r\n            />\r\n            Auto Capture\r\n          </label>\r\n\r\n          {autoCapture && (\r\n            <select\r\n              value={captureInterval}\r\n              onChange={(e) => setCaptureInterval(Number(e.target.value))}\r\n              disabled={!isStreaming}\r\n              className=\"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\"\r\n            >\r\n              <option value={3000}>Every 3s</option>\r\n              <option value={5000}>Every 5s</option>\r\n              <option value={10000}>Every 10s</option>\r\n            </select>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Container */}\r\n      <div className=\"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\">\r\n        {isStreaming ? (\r\n          <Webcam\r\n            ref={webcamRef}\r\n            audio={false}\r\n            screenshotFormat=\"image/jpeg\"\r\n            videoConstraints={videoConstraints}\r\n            className=\"w-full h-80 md:h-96 lg:h-[500px] object-cover\"\r\n          />\r\n        ) : (\r\n          <div className=\"w-full h-80 md:h-96 lg:h-[500px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\">\r\n            <div className=\"text-center text-white\">\r\n              <FaVideo className=\"h-16 w-16 mx-auto mb-4 text-gray-400\" />\r\n              <p className=\"text-lg font-medium\">Click \"Start Stream\" to begin video consultation</p>\r\n              <p className=\"text-sm text-gray-400 mt-2\">Live dental examination and analysis</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {isCapturing && (\r\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\r\n            <div className=\"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\">\r\n              <FaCamera className=\"mr-2 h-5 w-5 animate-pulse\" />\r\n              Capturing...\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recent Captures */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaImage className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recent Captures ({capturedImages.length})</h3>\r\n        </div>\r\n\r\n        {capturedImages.length > 0 ? (\r\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\r\n            {capturedImages.map((image) => (\r\n              <motion.div\r\n                key={image.id}\r\n                whileHover={{ scale: 1.05 }}\r\n                className=\"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\"\r\n              >\r\n                <img\r\n                  src={image.src}\r\n                  alt=\"Captured\"\r\n                  className=\"w-full h-24 object-cover\"\r\n                />\r\n                <div className=\"p-2\">\r\n                  <p className=\"text-xs text-gray-600 mb-1\">\r\n                    {new Date(image.timestamp).toLocaleTimeString()}\r\n                  </p>\r\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                    image.analyzed\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : 'bg-yellow-100 text-yellow-800'\r\n                  }`}>\r\n                    {image.analyzed ? '✓ Analyzed' : '⏳ Pending'}\r\n                  </span>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            <FaImage className=\"h-12 w-12 mx-auto mb-3 opacity-50\" />\r\n            <p>No captures yet. Start streaming and capture images for analysis.</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoCall; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,gBAAgB;AAClF,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,kBAAkB;EAAEC,eAAe;EAAEC,eAAe;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAClG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAMqC,SAAS,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqC,WAAW,GAAGrC,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMsC,mBAAmB,GAAGpC,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMqC,OAAO,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,gBAAgB,CAAC,CAAC;MAC/D,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC;MAE3EC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEL,YAAY,CAAC;MAC/CZ,mBAAmB,CAACY,YAAY,CAAC;;MAEjC;MACA,MAAMM,SAAS,GAAGN,YAAY,CAACO,IAAI,CAACL,MAAM,IACxCA,MAAM,CAACM,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAC1CR,MAAM,CAACM,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC/CR,MAAM,CAACM,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAC9C,CAAC;MAED,IAAIJ,SAAS,EAAE;QACbhB,iBAAiB,CAACgB,SAAS,CAACK,QAAQ,CAAC;QACrCP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,SAAS,CAACE,KAAK,CAAC;MACtD,CAAC,MAAM,IAAIR,YAAY,CAACY,MAAM,GAAG,CAAC,EAAE;QAClCtB,iBAAiB,CAACU,YAAY,CAAC,CAAC,CAAC,CAACW,QAAQ,CAAC;QAC3CP,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEL,YAAY,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC;MACxE;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CpD,KAAK,CAACoD,KAAK,CAAC,2BAA2B,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXL,QAAQ,EAAEtB,cAAc,GAAG;MAAE4B,KAAK,EAAE5B;IAAe,CAAC,GAAG6B;EACzD,CAAC;EAED,MAAMC,oBAAoB,GAAG5D,WAAW,CAAC,MAAO6D,QAAQ,IAAK;IAC3D,IAAI;MACF/C,eAAe,CAAC,CAAC;;MAEjB;MACA,MAAMgD,UAAU,GAAGD,QAAQ,CAACE,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;MACpE,MAAMC,IAAI,GAAG,MAAMC,KAAK,CAAC,0BAA0BH,UAAU,EAAE,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC;;MAExF;MACA,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEN,IAAI,EAAE,aAAa,CAAC;;MAE7C;MACA,MAAMO,QAAQ,GAAG,MAAMN,KAAK,CAAC,cAAc,EAAE;QAC3CO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEL;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,MAAMC,OAAO,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MACrChC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8B,OAAO,CAAC;MACvC,IAAI5D,kBAAkB,IAAI4D,OAAO,IAAIA,OAAO,CAACA,OAAO,EAAE;QACpD5D,kBAAkB,CAAC4D,OAAO,CAACA,OAAO,CAAC;MACrC;IAEF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDpD,KAAK,CAACoD,KAAK,CAAC,yBAAyB,CAAC;MACtC,IAAItC,kBAAkB,EAAE;QACtBA,kBAAkB,CAAC,EAAE,CAAC;MACxB;IACF;EACF,CAAC,EAAE,CAACF,eAAe,EAAEE,kBAAkB,CAAC,CAAC;EAEzC,MAAM8D,YAAY,GAAG9E,WAAW,CAAC,MAAM;IACrC,IAAIkC,SAAS,CAAC6C,OAAO,IAAI7D,WAAW,EAAE;MACpCG,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMwC,QAAQ,GAAG3B,SAAS,CAAC6C,OAAO,CAACC,aAAa,CAAC,CAAC;MAElD,IAAInB,QAAQ,EAAE;QACZ,MAAMoB,QAAQ,GAAG;UACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,GAAG,EAAExB,QAAQ;UACbyB,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;UACnCC,QAAQ,EAAE;QACZ,CAAC;QAEDjE,iBAAiB,CAACkE,IAAI,IAAI,CAACR,QAAQ,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5D;QACA,IAAI3E,eAAe,EAAE;UACnBA,eAAe,CAAC8C,QAAQ,CAAC;QAC3B;;QAEA;QACAD,oBAAoB,CAACC,QAAQ,CAAC;QAE9B3D,KAAK,CAACyF,OAAO,CAAC,sCAAsC,CAAC;MACvD;MAEAtE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,WAAW,EAAEH,eAAe,EAAE6C,oBAAoB,CAAC,CAAC;EAExD,MAAMgC,gBAAgB,GAAG5F,WAAW,CAAC,MAAM;IACzC,IAAImC,WAAW,CAAC4C,OAAO,EAAE;MACvBc,aAAa,CAAC1D,WAAW,CAAC4C,OAAO,CAAC;IACpC;IAEA5C,WAAW,CAAC4C,OAAO,GAAGe,WAAW,CAAC,MAAM;MACtC,IAAI5E,WAAW,IAAIgB,SAAS,CAAC6C,OAAO,EAAE;QACpCD,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,EAAEpD,eAAe,CAAC;EACrB,CAAC,EAAE,CAACR,WAAW,EAAEQ,eAAe,EAAEoD,YAAY,CAAC,CAAC;EAEhD,MAAMiB,WAAW,GAAG/F,WAAW,CAAC,MAAM;IACpC,IAAI,CAAC8B,cAAc,EAAE;MACnB5B,KAAK,CAACoD,KAAK,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEAnC,cAAc,CAAC,IAAI,CAAC;IACpBN,kBAAkB,CAAC,IAAI,CAAC;IACxBX,KAAK,CAACyF,OAAO,CAAC,sBAAsB,CAAC;;IAErC;IACA,IAAInE,WAAW,EAAE;MACfoE,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACpE,WAAW,EAAEX,kBAAkB,EAAE+E,gBAAgB,EAAE9D,cAAc,CAAC,CAAC;EAEvE,MAAMkE,UAAU,GAAGhG,WAAW,CAAC,MAAM;IACnCmB,cAAc,CAAC,KAAK,CAAC;IACrBN,kBAAkB,CAAC,KAAK,CAAC;IACzBoF,eAAe,CAAC,CAAC;IACjB/F,KAAK,CAACgG,IAAI,CAAC,sBAAsB,CAAC;EACpC,CAAC,EAAE,CAACrF,kBAAkB,CAAC,CAAC;EAExB,MAAMoF,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI9D,WAAW,CAAC4C,OAAO,EAAE;MACvBc,aAAa,CAAC1D,WAAW,CAAC4C,OAAO,CAAC;MAClC5C,WAAW,CAAC4C,OAAO,GAAG,IAAI;IAC5B;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1E,cAAc,CAAC,CAACD,WAAW,CAAC;IAC5B,IAAI,CAACA,WAAW,EAAE;MAChBoE,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMG,kBAAkB,GAAIhD,QAAQ,IAAK;IACvCrB,iBAAiB,CAACqB,QAAQ,CAAC;IAC3B,IAAIlC,WAAW,EAAE;MACf8E,UAAU,CAAC,CAAC;MACZK,UAAU,CAAC,MAAM;QACfN,WAAW,CAAC,CAAC;MACf,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EAEDhG,SAAS,CAAC,MAAM;IACdqC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EAEzBrC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXkG,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENlG,SAAS,CAAC,MAAM;IACd,IAAIyB,WAAW,IAAIN,WAAW,EAAE;MAC9B0E,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACzE,WAAW,EAAEE,eAAe,EAAER,WAAW,EAAE0E,gBAAgB,CAAC,CAAC;EAEjE,oBACEjF,OAAA;IAAK2F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5F,OAAA;MAAK2F,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC5F,OAAA;QAAK2F,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5F,OAAA;UAAI2F,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEhG,OAAA;UACEiG,OAAO,EAAEA,CAAA,KAAM3E,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;UAC1DsE,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAEzE5F,OAAA,CAACF,KAAK;YAAC6F,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjC3E,kBAAkB,GAAG,MAAM,GAAG,MAAM,EAAC,WACxC;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL3E,kBAAkB,iBACjBrB,OAAA;QAAK2F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5F,OAAA;UAAO2F,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRhG,OAAA;UACEkG,KAAK,EAAE/E,cAAc,IAAI,EAAG;UAC5BgF,QAAQ,EAAGC,CAAC,IAAKX,kBAAkB,CAACW,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDP,SAAS,EAAC,qHAAqH;UAAAC,QAAA,gBAE/H5F,OAAA;YAAQkG,KAAK,EAAC,EAAE;YAAAN,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAC3C/E,gBAAgB,CAACqF,GAAG,CAAEC,MAAM,iBAC3BvG,OAAA;YAA8BkG,KAAK,EAAEK,MAAM,CAAC9D,QAAS;YAAAmD,QAAA,EAClDW,MAAM,CAACjE,KAAK,IAAI,UAAUiE,MAAM,CAAC9D,QAAQ,CAACsC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UAAK,GADhDwB,MAAM,CAAC9D,QAAQ;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACThG,OAAA;UAAG2F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjC3E,gBAAgB,CAACyB,MAAM,EAAC,qBAC3B;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChE5F,OAAA;QAAK2F,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB5F,OAAA,CAACR,MAAM,CAACgH,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1Bf,SAAS,EAAE,oFACTpF,WAAW,GACP,mFAAmF,GACnF,mFAAmF,EACtF;UACH0F,OAAO,EAAE1F,WAAW,GAAG8E,UAAU,GAAGD,WAAY;UAChDwB,QAAQ,EAAE,CAACzF,cAAe;UAAAyE,QAAA,GAEzBrF,WAAW,gBAAGP,OAAA,CAACN,MAAM;YAACiG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACP,MAAM;YAACkG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvFzF,WAAW,GAAG,aAAa,GAAG,cAAc;QAAA;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEhBhG,OAAA,CAACR,MAAM,CAACgH,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1Bf,SAAS,EAAC,oNAAoN;UAC9NM,OAAO,EAAE9B,YAAa;UACtByC,QAAQ,EAAE,CAACrG,WAAW,IAAIE,WAAY;UAAAmF,QAAA,gBAEtC5F,OAAA,CAACL,QAAQ;YAACgG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACpCvF,WAAW,GAAG,cAAc,GAAG,eAAe;QAAA;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAENhG,OAAA;QAAK2F,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC5F,OAAA;UAAO2F,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBACpE5F,OAAA;YACE6G,IAAI,EAAC,UAAU;YACfC,OAAO,EAAEjG,WAAY;YACrBsF,QAAQ,EAAEX,iBAAkB;YAC5BoB,QAAQ,EAAE,CAACrG,WAAY;YACvBoF,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAEPnF,WAAW,iBACVb,OAAA;UACEkG,KAAK,EAAEnF,eAAgB;UACvBoF,QAAQ,EAAGC,CAAC,IAAKpF,kBAAkB,CAAC+F,MAAM,CAACX,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAAE;UAC5DU,QAAQ,EAAE,CAACrG,WAAY;UACvBoF,SAAS,EAAC,8GAA8G;UAAAC,QAAA,gBAExH5F,OAAA;YAAQkG,KAAK,EAAE,IAAK;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtChG,OAAA;YAAQkG,KAAK,EAAE,IAAK;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtChG,OAAA;YAAQkG,KAAK,EAAE,KAAM;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,2DAA2D;MAAAC,QAAA,GACvErF,WAAW,gBACVP,OAAA,CAACV,MAAM;QACL0H,GAAG,EAAEzF,SAAU;QACf0F,KAAK,EAAE,KAAM;QACbC,gBAAgB,EAAC,YAAY;QAC7BtE,gBAAgB,EAAEA,gBAAiB;QACnC+C,SAAS,EAAC;MAA+C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAEFhG,OAAA;QAAK2F,SAAS,EAAC,+GAA+G;QAAAC,QAAA,eAC5H5F,OAAA;UAAK2F,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC5F,OAAA,CAACH,OAAO;YAAC8F,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DhG,OAAA;YAAG2F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvFhG,OAAA;YAAG2F,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAvF,WAAW,iBACVT,OAAA;QAAK2F,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvF5F,OAAA;UAAK2F,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAC3F5F,OAAA,CAACL,QAAQ;YAACgG,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvD5F,OAAA;QAAK2F,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC5F,OAAA;UAAK2F,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1E5F,OAAA,CAACJ,OAAO;YAAC+F,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNhG,OAAA;UAAI2F,SAAS,EAAC,sCAAsC;UAAAC,QAAA,GAAC,mBAAiB,EAACjF,cAAc,CAAC+B,MAAM,EAAC,GAAC;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC,EAELrF,cAAc,CAAC+B,MAAM,GAAG,CAAC,gBACxB1C,OAAA;QAAK2F,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEjF,cAAc,CAAC2F,GAAG,CAAEa,KAAK,iBACxBnH,OAAA,CAACR,MAAM,CAAC4H,GAAG;UAETX,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5Bf,SAAS,EAAC,2HAA2H;UAAAC,QAAA,gBAErI5F,OAAA;YACE0E,GAAG,EAAEyC,KAAK,CAACzC,GAAI;YACf2C,GAAG,EAAC,UAAU;YACd1B,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACFhG,OAAA;YAAK2F,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB5F,OAAA;cAAG2F,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtC,IAAIpB,IAAI,CAAC2C,KAAK,CAACxC,SAAS,CAAC,CAAC2C,kBAAkB,CAAC;YAAC;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACJhG,OAAA;cAAM2F,SAAS,EAAE,uEACfwB,KAAK,CAACtC,QAAQ,GACV,6BAA6B,GAC7B,+BAA+B,EAClC;cAAAe,QAAA,EACAuB,KAAK,CAACtC,QAAQ,GAAG,YAAY,GAAG;YAAW;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GApBDmB,KAAK,CAAC5C,EAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBH,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENhG,OAAA;QAAK2F,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5F,OAAA,CAACJ,OAAO;UAAC+F,SAAS,EAAC;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDhG,OAAA;UAAA4F,QAAA,EAAG;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1F,EAAA,CA7WIL,SAAS;AAAAsH,EAAA,GAATtH,SAAS;AA+Wf,eAAeA,SAAS;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}