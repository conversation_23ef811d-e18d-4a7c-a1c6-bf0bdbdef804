{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ToastContainer, toast } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { motion } from 'framer-motion';\nimport { FaTooth, FaVideo, FaUser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt, FaServer } from 'react-icons/fa';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport Sidebar from './components/Sidebar';\nimport Navbar from './components/Navbar';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  var _detectionResults$;\n  const [isConnected, setIsConnected] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [serverStatus, setServerStatus] = useState('checking');\n  const [patientInfo] = useState({\n    name: 'John Doe',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\n  const [analysisHistory, setAnalysisHistory] = useState([]);\n\n  // Check server status\n  const checkServerStatus = async () => {\n    try {\n      const response = await fetch('/api/health');\n      if (response.ok) {\n        const data = await response.json();\n        setServerStatus('connected');\n        console.log('✅ YOLOv8 server connected:', data);\n      } else {\n        setServerStatus('error');\n        console.log('❌ YOLOv8 server health check failed');\n      }\n    } catch (error) {\n      setServerStatus('disconnected');\n      console.log('❌ YOLOv8 server not reachable:', error.message);\n    }\n  };\n  useEffect(() => {\n    checkServerStatus();\n    // Check server status every 30 seconds\n    const interval = setInterval(checkServerStatus, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const handleConnectionStatus = status => {\n    setIsConnected(status);\n    if (status) {\n      toast.success('Connected to patient successfully!');\n    } else {\n      toast.error('Connection lost. Trying to reconnect...');\n    }\n  };\n  const handleDetectionResults = results => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n\n    // Add to history\n    const newAnalysis = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentCapturedImage,\n      patientId: patientInfo.id,\n      patientName: patientInfo.name\n    };\n    setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\n\n    if (results.length > 0) {\n      const classNames = results.map(result => result.class);\n      toast.info(`Detected: ${classNames.join(', ')}`);\n    }\n  };\n  const startAnalysis = () => {\n    if (serverStatus !== 'connected') {\n      toast.error('YOLOv8 server not connected. Please start the backend server first.');\n      return;\n    }\n    setIsAnalyzing(true);\n    toast.info('Starting dental analysis...');\n  };\n  const handleImageCaptured = imageSrc => {\n    setCurrentCapturedImage(imageSrc);\n  };\n  const getServerStatusColor = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'text-green-600';\n      case 'disconnected':\n        return 'text-red-600';\n      case 'error':\n        return 'text-yellow-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  const getServerStatusText = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'YOLOv8 Connected';\n      case 'disconnected':\n        return 'YOLOv8 Disconnected';\n      case 'error':\n        return 'YOLOv8 Error';\n      default:\n        return 'Checking...';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen),\n        isConnected: isConnected\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Intraoral Patient Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Real-time dental analysis and consultation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getServerStatusColor()}`,\n                    children: [/*#__PURE__*/_jsxDEV(FaServer, {\n                      className: \"mr-1 h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this), getServerStatusText()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: startAnalysis,\n                  disabled: isAnalyzing || serverStatus !== 'connected',\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: [/*#__PURE__*/_jsxDEV(FaBrain, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this), isAnalyzing ? 'Analyzing...' : 'Start Analysis']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this), serverStatus !== 'connected' && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-red-600 text-center\",\n                  children: \"YOLOv8 server required for analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Patient\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: patientInfo.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Video Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: isConnected ? 'Live' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Detections\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: detectionResults.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Analyses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: analysisHistory.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Live Video Consultation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(VideoCall, {\n                  onConnectionStatus: handleConnectionStatus,\n                  onStartAnalysis: startAnalysis,\n                  onImageCaptured: handleImageCaptured\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Patient Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(PatientInfo, {\n                  patient: patientInfo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaBrain, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"AI Dental Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YOLODetection, {\n                  onResults: handleDetectionResults,\n                  isAnalyzing: isAnalyzing,\n                  currentImage: currentCapturedImage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Analysis Results\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnalysisResults, {\n                  results: detectionResults,\n                  isAnalyzing: isAnalyzing,\n                  history: analysisHistory\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.4\n              },\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-[#0077B6]\",\n                  children: \"Patient Summary & History\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Current Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Total Detections\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 330,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-lg font-bold text-[#0077B6]\",\n                          children: detectionResults.length\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 331,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 329,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Primary Issue\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 334,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-semibold text-gray-900 capitalize\",\n                          children: ((_detectionResults$ = detectionResults[0]) === null || _detectionResults$ === void 0 ? void 0 : _detectionResults$.class.replace('-', ' ')) || 'None'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 335,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 333,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2\",\n                      children: detectionResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700 capitalize\",\n                          children: result.class.replace('-', ' ')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 344,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-bold text-[#0077B6]\",\n                          children: [(result.confidence * 100).toFixed(1), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No current analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Start analysis to see summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: (() => {\n                      const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n                      const recommendations = [];\n                      if (decayCount > 0) {\n                        recommendations.push({\n                          type: 'urgent',\n                          title: 'Immediate Treatment',\n                          description: 'Cavities require immediate dental treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                            className: \"h-4 w-4 text-red-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 385,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (earlyDecayCount > 0) {\n                        recommendations.push({\n                          type: 'warning',\n                          title: 'Preventive Care',\n                          description: 'Schedule follow-up for preventive treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                            className: \"h-4 w-4 text-yellow-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 394,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (healthyCount > 0) {\n                        recommendations.push({\n                          type: 'positive',\n                          title: 'Good Oral Health',\n                          description: 'Continue regular oral hygiene routine.',\n                          icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                            className: \"h-4 w-4 text-green-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 403,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      recommendations.push({\n                        type: 'info',\n                        title: 'Regular Checkup',\n                        description: 'Schedule next checkup within 6 months.',\n                        icon: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                          className: \"h-4 w-4 text-blue-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 411,\n                          columnNumber: 35\n                        }, this)\n                      });\n                      return recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mr-3 mt-1\",\n                          children: rec.icon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 416,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: rec.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 420,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-gray-600\",\n                            children: rec.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 421,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 419,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 29\n                      }, this));\n                    })()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Complete analysis for recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 440,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Analysis History\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this), analysisHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                    children: [analysisHistory.slice(0, 5).map((historyItem, index) => {\n                      const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\n                      let severity = 'low';\n                      let icon = /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                        className: \"h-4 w-4 text-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 38\n                      }, this);\n                      if (decayCount > 0) {\n                        severity = 'high';\n                        icon = /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                          className: \"h-4 w-4 text-red-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 457,\n                          columnNumber: 36\n                        }, this);\n                      } else if (earlyDecayCount > 0) {\n                        severity = 'medium';\n                        icon = /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                          className: \"h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 460,\n                          columnNumber: 36\n                        }, this);\n                      }\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mr-3\",\n                            children: icon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 466,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm font-medium text-gray-900\",\n                              children: [historyItem.results.length, \" detection(s)\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 470,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-xs text-gray-500\",\n                              children: new Date(historyItem.timestamp).toLocaleDateString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 473,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 469,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 465,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\",\n                          children: severity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 478,\n                          columnNumber: 31\n                        }, this)]\n                      }, historyItem.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 464,\n                        columnNumber: 29\n                      }, this);\n                    }), analysisHistory.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center pt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\"+\", analysisHistory.length - 5, \" more analyses\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No analysis history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Perform analyses to build history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"TRXlZPZ5ZLLb4P/JXy7DLB02iIo=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ToastContainer", "toast", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaVideo", "FaUser", "FaChartBar", "FaBrain", "FaClock", "FaEye", "FaSearch", "FaHistory", "FaInfoCircle", "FaTimesCircle", "FaExclamationTriangle", "FaCheckCircle", "FaCalendarAlt", "FaServer", "VideoCall", "YOLODetection", "PatientInfo", "AnalysisResults", "Sidebar", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "_detectionResults$", "isConnected", "setIsConnected", "sidebarOpen", "setSidebarOpen", "serverStatus", "setServerStatus", "patientInfo", "name", "id", "age", "lastVisit", "detectionResults", "setDetectionResults", "isAnalyzing", "setIsAnalyzing", "currentCapturedImage", "setCurrentCapturedImage", "analysisHistory", "setAnalysisHistory", "checkServerStatus", "response", "fetch", "ok", "data", "json", "console", "log", "error", "message", "interval", "setInterval", "clearInterval", "handleConnectionStatus", "status", "success", "handleDetectionResults", "results", "newAnalysis", "Date", "now", "timestamp", "toISOString", "image", "patientId", "patientName", "prev", "slice", "length", "classNames", "map", "result", "class", "info", "join", "startAnalysis", "handleImageCaptured", "imageSrc", "getServerStatusColor", "getServerStatusText", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "opacity", "animate", "transition", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "disabled", "y", "delay", "x", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "patient", "onResults", "currentImage", "history", "replace", "index", "confidence", "toFixed", "decayCount", "filter", "r", "earlyDecayCount", "healthyCount", "recommendations", "push", "type", "title", "description", "icon", "rec", "historyItem", "severity", "toLocaleDateString", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ToastContainer, toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport { motion } from 'framer-motion';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaVideo, FaUser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt, FaServer } from 'react-icons/fa';\r\nimport VideoCall from './components/VideoCall';\r\nimport YOLODetection from './components/YOLODetection';\r\nimport PatientInfo from './components/PatientInfo';\r\nimport AnalysisResults from './components/AnalysisResults';\r\nimport Sidebar from './components/Sidebar';\r\nimport Navbar from './components/Navbar';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [serverStatus, setServerStatus] = useState('checking');\r\n  const [patientInfo] = useState({\r\n    name: 'John Doe',\r\n    id: 'P001',\r\n    age: 35,\r\n    lastVisit: '2024-01-15'\r\n  });\r\n  const [detectionResults, setDetectionResults] = useState([]);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\r\n  const [analysisHistory, setAnalysisHistory] = useState([]);\r\n\r\n  // Check server status\r\n  const checkServerStatus = async () => {\r\n    try {\r\n      const response = await fetch('/api/health');\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setServerStatus('connected');\r\n        console.log('✅ YOLOv8 server connected:', data);\r\n      } else {\r\n        setServerStatus('error');\r\n        console.log('❌ YOLOv8 server health check failed');\r\n      }\r\n    } catch (error) {\r\n      setServerStatus('disconnected');\r\n      console.log('❌ YOLOv8 server not reachable:', error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    checkServerStatus();\r\n    // Check server status every 30 seconds\r\n    const interval = setInterval(checkServerStatus, 30000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const handleConnectionStatus = (status) => {\r\n    setIsConnected(status);\r\n    if (status) {\r\n      toast.success('Connected to patient successfully!');\r\n    } else {\r\n      toast.error('Connection lost. Trying to reconnect...');\r\n    }\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    setDetectionResults(results);\r\n    setIsAnalyzing(false);\r\n    \r\n    // Add to history\r\n    const newAnalysis = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentCapturedImage,\r\n      patientId: patientInfo.id,\r\n      patientName: patientInfo.name\r\n    };\r\n    \r\n    setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\r\n    \r\n    if (results.length > 0) {\r\n      const classNames = results.map(result => result.class);\r\n      toast.info(`Detected: ${classNames.join(', ')}`);\r\n    }\r\n  };\r\n\r\n  const startAnalysis = () => {\r\n    if (serverStatus !== 'connected') {\r\n      toast.error('YOLOv8 server not connected. Please start the backend server first.');\r\n      return;\r\n    }\r\n    \r\n    setIsAnalyzing(true);\r\n    toast.info('Starting dental analysis...');\r\n  };\r\n\r\n  const handleImageCaptured = (imageSrc) => {\r\n    setCurrentCapturedImage(imageSrc);\r\n  };\r\n\r\n  const getServerStatusColor = () => {\r\n    switch (serverStatus) {\r\n      case 'connected': return 'text-green-600';\r\n      case 'disconnected': return 'text-red-600';\r\n      case 'error': return 'text-yellow-600';\r\n      default: return 'text-gray-600';\r\n    }\r\n  };\r\n\r\n  const getServerStatusText = () => {\r\n    switch (serverStatus) {\r\n      case 'connected': return 'YOLOv8 Connected';\r\n      case 'disconnected': return 'YOLOv8 Disconnected';\r\n      case 'error': return 'YOLOv8 Error';\r\n      default: return 'Checking...';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isConnected={isConnected} />\r\n\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              {/* Header */}\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Intraoral Patient Dashboard\r\n                  </h1>\r\n                  <p className=\"text-[#333333]\">Real-time dental analysis and consultation</p>\r\n                  <div className=\"flex items-center mt-2\">\r\n                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getServerStatusColor()}`}>\r\n                      <FaServer className=\"mr-1 h-3 w-3\" />\r\n                      {getServerStatusText()}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex flex-col gap-3\">\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    onClick={startAnalysis}\r\n                    disabled={isAnalyzing || serverStatus !== 'connected'}\r\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    <FaBrain className=\"h-5 w-5 mr-2\" />\r\n                    {isAnalyzing ? 'Analyzing...' : 'Start Analysis'}\r\n                  </motion.button>\r\n                  {serverStatus !== 'connected' && (\r\n                    <p className=\"text-xs text-red-600 text-center\">\r\n                      YOLOv8 server required for analysis\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Stats Cards */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.1 }}\r\n                className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\r\n              >\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaUser className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Patient</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{patientInfo.name}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaVideo className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Video Status</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{isConnected ? 'Live' : 'Offline'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaEye className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Detections</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{detectionResults.length}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaHistory className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Total Analyses</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{analysisHistory.length}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Main Content - Split Layout */}\r\n              <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\">\r\n                {/* Live Video Section - Larger */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaVideo className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Live Video Consultation</h2>\r\n                  </div>\r\n                  <VideoCall\r\n                    onConnectionStatus={handleConnectionStatus}\r\n                    onStartAnalysis={startAnalysis}\r\n                    onImageCaptured={handleImageCaptured}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Patient Info Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: 20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaUser className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Patient Information</h2>\r\n                  </div>\r\n                  <PatientInfo patient={patientInfo} />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Analysis and Results Section */}\r\n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\r\n                {/* AI Analysis Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaBrain className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">AI Dental Analysis</h2>\r\n                  </div>\r\n                  <YOLODetection\r\n                    onResults={handleDetectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                    currentImage={currentCapturedImage}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Results Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaChartBar className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Analysis Results</h2>\r\n                  </div>\r\n                  <AnalysisResults\r\n                    results={detectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                    history={analysisHistory}\r\n                  />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Static Patient Summary Section - Always Visible */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.4 }}\r\n                className=\"mt-8\"\r\n              >\r\n                <div className=\"flex items-center mb-6\">\r\n                  <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                    <FaHistory className=\"h-5 w-5\" />\r\n                  </div>\r\n                  <h2 className=\"text-2xl font-bold text-[#0077B6]\">Patient Summary & History</h2>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n                  {/* Summary Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaChartBar className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Current Summary</h3>\r\n                    </div>\r\n                    \r\n                    {detectionResults.length > 0 ? (\r\n                      <div className=\"space-y-4\">\r\n                        <div className=\"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\">\r\n                          <div className=\"flex items-center justify-between mb-2\">\r\n                            <span className=\"text-sm font-medium text-gray-700\">Total Detections</span>\r\n                            <span className=\"text-lg font-bold text-[#0077B6]\">{detectionResults.length}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span className=\"text-sm font-medium text-gray-700\">Primary Issue</span>\r\n                            <span className=\"text-sm font-semibold text-gray-900 capitalize\">\r\n                              {detectionResults[0]?.class.replace('-', ' ') || 'None'}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <div className=\"space-y-2\">\r\n                          {detectionResults.map((result, index) => (\r\n                            <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\r\n                              <span className=\"text-sm font-medium text-gray-700 capitalize\">\r\n                                {result.class.replace('-', ' ')}\r\n                              </span>\r\n                              <span className=\"text-sm font-bold text-[#0077B6]\">\r\n                                {(result.confidence * 100).toFixed(1)}%\r\n                              </span>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaSearch className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No current analysis</p>\r\n                        <p className=\"text-gray-500 text-sm\">Start analysis to see summary</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Recommendations Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaInfoCircle className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recommendations</h3>\r\n                    </div>\r\n                    \r\n                    {detectionResults.length > 0 ? (\r\n                      <div className=\"space-y-3\">\r\n                        {(() => {\r\n                          const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n                          const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n                          const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\r\n                          const recommendations = [];\r\n\r\n                          if (decayCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'urgent',\r\n                              title: 'Immediate Treatment',\r\n                              description: 'Cavities require immediate dental treatment.',\r\n                              icon: <FaTimesCircle className=\"h-4 w-4 text-red-500\" />\r\n                            });\r\n                          }\r\n\r\n                          if (earlyDecayCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'warning',\r\n                              title: 'Preventive Care',\r\n                              description: 'Schedule follow-up for preventive treatment.',\r\n                              icon: <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />\r\n                            });\r\n                          }\r\n\r\n                          if (healthyCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'positive',\r\n                              title: 'Good Oral Health',\r\n                              description: 'Continue regular oral hygiene routine.',\r\n                              icon: <FaCheckCircle className=\"h-4 w-4 text-green-500\" />\r\n                            });\r\n                          }\r\n\r\n                          recommendations.push({\r\n                            type: 'info',\r\n                            title: 'Regular Checkup',\r\n                            description: 'Schedule next checkup within 6 months.',\r\n                            icon: <FaCalendarAlt className=\"h-4 w-4 text-blue-500\" />\r\n                          });\r\n\r\n                          return recommendations.map((rec, index) => (\r\n                            <div key={index} className=\"flex items-start p-3 bg-gray-50 rounded-lg\">\r\n                              <div className=\"mr-3 mt-1\">\r\n                                {rec.icon}\r\n                              </div>\r\n                              <div>\r\n                                <h4 className=\"text-sm font-semibold text-gray-900\">{rec.title}</h4>\r\n                                <p className=\"text-xs text-gray-600\">{rec.description}</p>\r\n                              </div>\r\n                            </div>\r\n                          ));\r\n                        })()}\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaInfoCircle className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No recommendations</p>\r\n                        <p className=\"text-gray-500 text-sm\">Complete analysis for recommendations</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Analysis History Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaHistory className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Analysis History</h3>\r\n                    </div>\r\n                    \r\n                    {analysisHistory.length > 0 ? (\r\n                      <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n                        {analysisHistory.slice(0, 5).map((historyItem, index) => {\r\n                          const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\r\n                          const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\r\n                          const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\r\n                          \r\n                          let severity = 'low';\r\n                          let icon = <FaCheckCircle className=\"h-4 w-4 text-green-500\" />;\r\n                          \r\n                          if (decayCount > 0) {\r\n                            severity = 'high';\r\n                            icon = <FaTimesCircle className=\"h-4 w-4 text-red-500\" />;\r\n                          } else if (earlyDecayCount > 0) {\r\n                            severity = 'medium';\r\n                            icon = <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />;\r\n                          }\r\n\r\n                          return (\r\n                            <div key={historyItem.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                              <div className=\"flex items-center\">\r\n                                <div className=\"mr-3\">\r\n                                  {icon}\r\n                                </div>\r\n                                <div>\r\n                                  <p className=\"text-sm font-medium text-gray-900\">\r\n                                    {historyItem.results.length} detection(s)\r\n                                  </p>\r\n                                  <p className=\"text-xs text-gray-500\">\r\n                                    {new Date(historyItem.timestamp).toLocaleDateString()}\r\n                                  </p>\r\n                                </div>\r\n                              </div>\r\n                              <span className=\"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\">\r\n                                {severity}\r\n                              </span>\r\n                            </div>\r\n                          );\r\n                        })}\r\n                        \r\n                        {analysisHistory.length > 5 && (\r\n                          <div className=\"text-center pt-2\">\r\n                            <p className=\"text-xs text-gray-500\">\r\n                              +{analysisHistory.length - 5} more analyses\r\n                            </p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaHistory className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No analysis history</p>\r\n                        <p className=\"text-gray-500 text-sm\">Perform analyses to build history</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      <ToastContainer\r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop={false}\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme=\"light\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/M,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,UAAU,CAAC;EAC5D,MAAM,CAACqC,WAAW,CAAC,GAAGrC,QAAQ,CAAC;IAC7BsC,IAAI,EAAE,UAAU;IAChBC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMkD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAa,CAAC;MAC3C,IAAID,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCnB,eAAe,CAAC,WAAW,CAAC;QAC5BoB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEH,IAAI,CAAC;MACjD,CAAC,MAAM;QACLlB,eAAe,CAAC,OAAO,CAAC;QACxBoB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdtB,eAAe,CAAC,cAAc,CAAC;MAC/BoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,KAAK,CAACC,OAAO,CAAC;IAC9D;EACF,CAAC;EAED1D,SAAS,CAAC,MAAM;IACdiD,iBAAiB,CAAC,CAAC;IACnB;IACA,MAAMU,QAAQ,GAAGC,WAAW,CAACX,iBAAiB,EAAE,KAAK,CAAC;IACtD,OAAO,MAAMY,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,sBAAsB,GAAIC,MAAM,IAAK;IACzChC,cAAc,CAACgC,MAAM,CAAC;IACtB,IAAIA,MAAM,EAAE;MACV7D,KAAK,CAAC8D,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC,MAAM;MACL9D,KAAK,CAACuD,KAAK,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;EAED,MAAMQ,sBAAsB,GAAIC,OAAO,IAAK;IAC1CxB,mBAAmB,CAACwB,OAAO,CAAC;IAC5BtB,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,MAAMuB,WAAW,GAAG;MAClB7B,EAAE,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCL,OAAO,EAAEA,OAAO;MAChBM,KAAK,EAAE3B,oBAAoB;MAC3B4B,SAAS,EAAErC,WAAW,CAACE,EAAE;MACzBoC,WAAW,EAAEtC,WAAW,CAACC;IAC3B,CAAC;IAEDW,kBAAkB,CAAC2B,IAAI,IAAI,CAACR,WAAW,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjE,IAAIV,OAAO,CAACW,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,UAAU,GAAGZ,OAAO,CAACa,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MACtD/E,KAAK,CAACgF,IAAI,CAAC,aAAaJ,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIlD,YAAY,KAAK,WAAW,EAAE;MAChChC,KAAK,CAACuD,KAAK,CAAC,qEAAqE,CAAC;MAClF;IACF;IAEAb,cAAc,CAAC,IAAI,CAAC;IACpB1C,KAAK,CAACgF,IAAI,CAAC,6BAA6B,CAAC;EAC3C,CAAC;EAED,MAAMG,mBAAmB,GAAIC,QAAQ,IAAK;IACxCxC,uBAAuB,CAACwC,QAAQ,CAAC;EACnC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQrD,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,gBAAgB;MACzC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,OAAO;QAAE,OAAO,iBAAiB;MACtC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAMsD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQtD,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,cAAc;QAAE,OAAO,qBAAqB;MACjD,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC;QAAS,OAAO,aAAa;IAC/B;EACF,CAAC;EAED,oBACER,OAAA;IAAK+D,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvChE,OAAA,CAACH,OAAO;MAACoE,MAAM,EAAE3D,WAAY;MAAC4D,SAAS,EAAE3D;IAAe;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3DtE,OAAA;MAAK+D,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDhE,OAAA,CAACF,MAAM;QAACyE,aAAa,EAAEA,CAAA,KAAMhE,cAAc,CAAC,CAACD,WAAW,CAAE;QAACF,WAAW,EAAEA;MAAY;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvFtE,OAAA;QAAM+D,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGhE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChChE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAG9BhE,OAAA;cAAK+D,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FhE,OAAA;gBAAAgE,QAAA,gBACEhE,OAAA;kBAAI+D,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtE,OAAA;kBAAG+D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5EtE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrChE,OAAA;oBAAM+D,SAAS,EAAE,uEAAuEF,oBAAoB,CAAC,CAAC,EAAG;oBAAAG,QAAA,gBAC/GhE,OAAA,CAACR,QAAQ;sBAACuE,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACpCR,mBAAmB,CAAC,CAAC;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtE,OAAA;gBAAK+D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClChE,OAAA,CAACvB,MAAM,CAACqG,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAExB,aAAc;kBACvByB,QAAQ,EAAElE,WAAW,IAAIT,YAAY,KAAK,WAAY;kBACtDuD,SAAS,EAAC,oPAAoP;kBAAAC,QAAA,gBAE9PhE,OAAA,CAAClB,OAAO;oBAACiF,SAAS,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCrD,WAAW,GAAG,cAAc,GAAG,gBAAgB;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACf9D,YAAY,KAAK,WAAW,iBAC3BR,OAAA;kBAAG+D,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEhD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErEhE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAACpB,MAAM;sBAACmF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9DtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEtD,WAAW,CAACC;oBAAI;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAACrB,OAAO;sBAACoF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE5D,WAAW,GAAG,MAAM,GAAG;oBAAS;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAAChB,KAAK;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjEtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEjD,gBAAgB,CAACoC;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAACd,SAAS;sBAAC6E,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE3C,eAAe,CAAC8B;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbtE,OAAA;cAAK+D,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDhE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,iIAAiI;gBAAAC,QAAA,gBAE3IhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAACrB,OAAO;sBAACoF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNtE,OAAA,CAACP,SAAS;kBACR8F,kBAAkB,EAAEnD,sBAAuB;kBAC3CoD,eAAe,EAAE9B,aAAc;kBAC/B+B,eAAe,EAAE9B;gBAAoB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAG,CAAE;gBAC/BX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAACpB,MAAM;sBAACmF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNtE,OAAA,CAACL,WAAW;kBAAC+F,OAAO,EAAEhF;gBAAY;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNtE,OAAA;cAAK+D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDhE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAAClB,OAAO;sBAACiF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNtE,OAAA,CAACN,aAAa;kBACZiG,SAAS,EAAEpD,sBAAuB;kBAClCtB,WAAW,EAAEA,WAAY;kBACzB2E,YAAY,EAAEzE;gBAAqB;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAACnB,UAAU;sBAACkF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNtE,OAAA,CAACJ,eAAe;kBACd4C,OAAO,EAAEzB,gBAAiB;kBAC1BE,WAAW,EAAEA,WAAY;kBACzB4E,OAAO,EAAExE;gBAAgB;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhBhE,OAAA;gBAAK+D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrChE,OAAA;kBAAK+D,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,eAC1EhE,OAAA,CAACd,SAAS;oBAAC6E,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACNtE,OAAA;kBAAI+D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAEpDhE,OAAA;kBAAK+D,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIhE,OAAA;oBAAK+D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrChE,OAAA;sBAAK+D,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EhE,OAAA,CAACnB,UAAU;wBAACkF,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACNtE,OAAA;sBAAI+D,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAELvD,gBAAgB,CAACoC,MAAM,GAAG,CAAC,gBAC1BnD,OAAA;oBAAK+D,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBhE,OAAA;sBAAK+D,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,gBACvDhE,OAAA;wBAAK+D,SAAS,EAAC,wCAAwC;wBAAAC,QAAA,gBACrDhE,OAAA;0BAAM+D,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC3EtE,OAAA;0BAAM+D,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,EAAEjD,gBAAgB,CAACoC;wBAAM;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC,eACNtE,OAAA;wBAAK+D,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDhE,OAAA;0BAAM+D,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAa;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxEtE,OAAA;0BAAM+D,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,EAC7D,EAAA7D,kBAAA,GAAAY,gBAAgB,CAAC,CAAC,CAAC,cAAAZ,kBAAA,uBAAnBA,kBAAA,CAAqBoD,KAAK,CAACuC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI;wBAAM;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENtE,OAAA;sBAAK+D,SAAS,EAAC,WAAW;sBAAAC,QAAA,EACvBjD,gBAAgB,CAACsC,GAAG,CAAC,CAACC,MAAM,EAAEyC,KAAK,kBAClC/F,OAAA;wBAAiB+D,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACnFhE,OAAA;0BAAM+D,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,EAC3DV,MAAM,CAACC,KAAK,CAACuC,OAAO,CAAC,GAAG,EAAE,GAAG;wBAAC;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACPtE,OAAA;0BAAM+D,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,GAC/C,CAACV,MAAM,CAAC0C,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;wBAAA;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,GANCyB,KAAK;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOV,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAENtE,OAAA;oBAAK+D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BhE,OAAA,CAACf,QAAQ;sBAAC8E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7DtE,OAAA;sBAAG+D,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEtE,OAAA;sBAAG+D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNtE,OAAA;kBAAK+D,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIhE,OAAA;oBAAK+D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrChE,OAAA;sBAAK+D,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EhE,OAAA,CAACb,YAAY;wBAAC4E,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACNtE,OAAA;sBAAI+D,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAELvD,gBAAgB,CAACoC,MAAM,GAAG,CAAC,gBAC1BnD,OAAA;oBAAK+D,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACvB,CAAC,MAAM;sBACN,MAAMkC,UAAU,GAAGnF,gBAAgB,CAACoF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACjF,MAAMkD,eAAe,GAAGtF,gBAAgB,CAACoF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACtF,MAAMmD,YAAY,GAAGvF,gBAAgB,CAACoF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAK,eAAe,CAAC,CAACJ,MAAM;sBACrF,MAAMoD,eAAe,GAAG,EAAE;sBAE1B,IAAIL,UAAU,GAAG,CAAC,EAAE;wBAClBK,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,QAAQ;0BACdC,KAAK,EAAE,qBAAqB;0BAC5BC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE5G,OAAA,CAACZ,aAAa;4BAAC2E,SAAS,EAAC;0BAAsB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACzD,CAAC,CAAC;sBACJ;sBAEA,IAAI+B,eAAe,GAAG,CAAC,EAAE;wBACvBE,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,iBAAiB;0BACxBC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE5G,OAAA,CAACX,qBAAqB;4BAAC0E,SAAS,EAAC;0BAAyB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACpE,CAAC,CAAC;sBACJ;sBAEA,IAAIgC,YAAY,GAAG,CAAC,EAAE;wBACpBC,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,UAAU;0BAChBC,KAAK,EAAE,kBAAkB;0BACzBC,WAAW,EAAE,wCAAwC;0BACrDC,IAAI,eAAE5G,OAAA,CAACV,aAAa;4BAACyE,SAAS,EAAC;0BAAwB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAC3D,CAAC,CAAC;sBACJ;sBAEAiC,eAAe,CAACC,IAAI,CAAC;wBACnBC,IAAI,EAAE,MAAM;wBACZC,KAAK,EAAE,iBAAiB;wBACxBC,WAAW,EAAE,wCAAwC;wBACrDC,IAAI,eAAE5G,OAAA,CAACT,aAAa;0BAACwE,SAAS,EAAC;wBAAuB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAC1D,CAAC,CAAC;sBAEF,OAAOiC,eAAe,CAAClD,GAAG,CAAC,CAACwD,GAAG,EAAEd,KAAK,kBACpC/F,OAAA;wBAAiB+D,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACrEhE,OAAA;0BAAK+D,SAAS,EAAC,WAAW;0BAAAC,QAAA,EACvB6C,GAAG,CAACD;wBAAI;0BAAAzC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC,eACNtE,OAAA;0BAAAgE,QAAA,gBACEhE,OAAA;4BAAI+D,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAE6C,GAAG,CAACH;0BAAK;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpEtE,OAAA;4BAAG+D,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAE6C,GAAG,CAACF;0BAAW;4BAAAxC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD,CAAC;sBAAA,GAPEyB,KAAK;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQV,CACN,CAAC;oBACJ,CAAC,EAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,gBAENtE,OAAA;oBAAK+D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BhE,OAAA,CAACb,YAAY;sBAAC4E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjEtE,OAAA;sBAAG+D,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpEtE,OAAA;sBAAG+D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAqC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNtE,OAAA;kBAAK+D,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIhE,OAAA;oBAAK+D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrChE,OAAA;sBAAK+D,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EhE,OAAA,CAACd,SAAS;wBAAC6E,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACNtE,OAAA;sBAAI+D,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,EAELjD,eAAe,CAAC8B,MAAM,GAAG,CAAC,gBACzBnD,OAAA;oBAAK+D,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAChD3C,eAAe,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACG,GAAG,CAAC,CAACyD,WAAW,EAAEf,KAAK,KAAK;sBACvD,MAAMG,UAAU,GAAGY,WAAW,CAACtE,OAAO,CAAC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACpF,MAAMkD,eAAe,GAAGS,WAAW,CAACtE,OAAO,CAAC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACzF,MAAMmD,YAAY,GAAGQ,WAAW,CAACtE,OAAO,CAAC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAK,eAAe,CAAC,CAACJ,MAAM;sBAExF,IAAI4D,QAAQ,GAAG,KAAK;sBACpB,IAAIH,IAAI,gBAAG5G,OAAA,CAACV,aAAa;wBAACyE,SAAS,EAAC;sBAAwB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;sBAE/D,IAAI4B,UAAU,GAAG,CAAC,EAAE;wBAClBa,QAAQ,GAAG,MAAM;wBACjBH,IAAI,gBAAG5G,OAAA,CAACZ,aAAa;0BAAC2E,SAAS,EAAC;wBAAsB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAC3D,CAAC,MAAM,IAAI+B,eAAe,GAAG,CAAC,EAAE;wBAC9BU,QAAQ,GAAG,QAAQ;wBACnBH,IAAI,gBAAG5G,OAAA,CAACX,qBAAqB;0BAAC0E,SAAS,EAAC;wBAAyB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBACtE;sBAEA,oBACEtE,OAAA;wBAA0B+D,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,gBAC/FhE,OAAA;0BAAK+D,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChChE,OAAA;4BAAK+D,SAAS,EAAC,MAAM;4BAAAC,QAAA,EAClB4C;0BAAI;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACNtE,OAAA;4BAAAgE,QAAA,gBACEhE,OAAA;8BAAG+D,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,GAC7C8C,WAAW,CAACtE,OAAO,CAACW,MAAM,EAAC,eAC9B;4BAAA;8BAAAgB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC,eACJtE,OAAA;8BAAG+D,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EACjC,IAAItB,IAAI,CAACoE,WAAW,CAAClE,SAAS,CAAC,CAACoE,kBAAkB,CAAC;4BAAC;8BAAA7C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNtE,OAAA;0BAAM+D,SAAS,EAAC,oFAAoF;0BAAAC,QAAA,EACjG+C;wBAAQ;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,GAhBCwC,WAAW,CAAClG,EAAE;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiBnB,CAAC;oBAEV,CAAC,CAAC,EAEDjD,eAAe,CAAC8B,MAAM,GAAG,CAAC,iBACzBnD,OAAA;sBAAK+D,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BhE,OAAA;wBAAG+D,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAAC3C,eAAe,CAAC8B,MAAM,GAAG,CAAC,EAAC,gBAC/B;sBAAA;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENtE,OAAA;oBAAK+D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BhE,OAAA,CAACd,SAAS;sBAAC6E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9DtE,OAAA;sBAAG+D,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEtE,OAAA;sBAAG+D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAiC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENtE,OAAA,CAACzB,cAAc;MACb0I,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAvD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACpE,EAAA,CA5fQD,GAAG;AAAA0H,EAAA,GAAH1H,GAAG;AA8fZ,eAAeA,GAAG;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}