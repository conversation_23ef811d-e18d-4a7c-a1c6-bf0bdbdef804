const express = require('express');
const router = express.Router();
const {
  createLabRequest,
  getStudentLabRequests,
  getAllLabRequests,
  updateLabRequest
} = require('../controllers/labRequestController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Student routes
router.post('/', auth, role('student'), createLabRequest);
router.get('/student', auth, role('student'), getStudentLabRequests);

// Assistant and Admin routes
router.get('/', auth, role('assistant', 'admin'), getAllLabRequests);
router.put('/:requestId', auth, role('assistant'), updateLabRequest);

module.exports = router;
