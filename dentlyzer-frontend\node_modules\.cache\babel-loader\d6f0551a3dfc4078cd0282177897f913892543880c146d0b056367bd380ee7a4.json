{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\PatientNav.jsx\",\n  _s = $RefreshSig$();\nimport { NavLink, useParams } from 'react-router-dom';\nimport { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaFileAlt, FaHistory, FaTooth, FaImages, FaCalendarAlt, FaFileSignature, FaClipboardCheck, FaFlask, FaUniversity, FaBuilding, FaTimes, FaCheck } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PatientNav = ({\n  selectedChart,\n  setSelectedChart,\n  charts\n}) => {\n  _s();\n  const {\n    nationalId\n  } = useParams();\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showLabPopup, setShowLabPopup] = useState(false);\n  const [selectedLabType, setSelectedLabType] = useState('');\n  const [showSubmitForm, setShowSubmitForm] = useState(false);\n  const [notes, setNotes] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n  const [labRequests, setLabRequests] = useState([]);\n  const [showConfirmation, setShowConfirmation] = useState(false);\n  const [confirmationMessage, setConfirmationMessage] = useState('');\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      try {\n        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);\n        setPatientData(response.data);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data;\n        console.error('Error fetching patient data:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status, (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data);\n        const message = ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 404 ? 'Patient not found' : ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to load patient data';\n        setError(message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPatientData();\n    if (showLabPopup) {\n      fetchLabRequests();\n    }\n  }, [nationalId, showLabPopup]);\n  const fetchLabRequests = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:5000/api/lab-requests/student', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      // Filter requests for current patient\n      const patientRequests = response.data.filter(req => req.patientId === nationalId);\n      setLabRequests(patientRequests);\n    } catch (error) {\n      console.error('Error fetching lab requests:', error);\n    }\n  };\n  const handleLabTypeSelect = labType => {\n    setSelectedLabType(labType);\n    setShowSubmitForm(true);\n  };\n  const handleSubmitRequest = async () => {\n    if (!selectedLabType || !patientData) return;\n    setSubmitting(true);\n    try {\n      const token = localStorage.getItem('token');\n      const requestData = {\n        patientId: nationalId,\n        patientName: patientData.fullName,\n        labType: selectedLabType,\n        notes: notes\n      };\n      await axios.post('http://localhost:5000/api/lab-requests', requestData, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Reset form\n      setSelectedLabType('');\n      setShowSubmitForm(false);\n      setNotes('');\n\n      // Refresh lab requests\n      fetchLabRequests();\n\n      // Show confirmation popup\n      setConfirmationMessage('Lab request submitted successfully!');\n      setShowConfirmation(true);\n    } catch (error) {\n      console.error('Error submitting lab request:', error);\n      setConfirmationMessage('Error submitting lab request. Please try again.');\n      setShowConfirmation(true);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'pending':\n        return 'text-orange-600 bg-orange-100';\n      case 'approved':\n        return 'text-green-600 bg-green-100';\n      case 'rejected':\n        return 'text-red-600 bg-red-100';\n      case 'completed':\n        return 'text-blue-600 bg-blue-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'approved':\n        return /*#__PURE__*/_jsxDEV(FaCheck, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 31\n        }, this);\n      case 'rejected':\n        return /*#__PURE__*/_jsxDEV(FaTimes, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 31\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaFlask, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-100 px-4 py-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rounded-full bg-gray-200 h-10 w-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-32\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-24\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !patientData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-100 px-4 py-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-500\",\n          children: error || 'Patient not found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this);\n  }\n  const renderChartSelector = isActive => {\n    if (!isActive || !charts || charts.length === 0) return null;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -10\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"ml-2\",\n      children: /*#__PURE__*/_jsxDEV(\"select\", {\n        value: (selectedChart === null || selectedChart === void 0 ? void 0 : selectedChart._id) || '',\n        onChange: e => setSelectedChart(charts.find(chart => chart._id === e.target.value)),\n        className: \"px-3 py-1.5 rounded-lg bg-white border border-gray-200 text-sm text-gray-600 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-200\",\n        children: charts.map(chart => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: chart._id,\n          children: [chart.title, \" - \", new Date(chart.date).toLocaleDateString(), \" \", chart.isLocked ? '(Locked)' : '']\n        }, chart._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.3\n      },\n      className: \"bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)] px-4 py-3 overflow-x-auto w-full\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center justify-between gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(NavLink, {\n          to: `/patientprofile/${nationalId}`,\n          className: \"flex items-center w-full md:w-auto hover:opacity-80 transition-opacity\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[rgba(0,119,182,0.1)] p-2 rounded-lg mr-3 flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6 text-[#0077B6]\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Current Patient\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg font-semibold text-gray-800 truncate\",\n              children: patientData.fullName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-auto mx-auto flex flex-col md:flex-row items-center gap-2\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"flex flex-wrap justify-center gap-1 md:gap-2 w-full overflow-x-auto py-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: `/patientprofile/${nationalId}/sheets`,\n                className: ({\n                  isActive\n                }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"Patient Examination Sheet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: `/patientprofile/${nationalId}/history`,\n                className: ({\n                  isActive\n                }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(FaHistory, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"History\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(NavLink, {\n                to: `/patientprofile/${nationalId}/toothchart`,\n                className: ({\n                  isActive\n                }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(FaTooth, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"Tooth Chart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 15\n              }, this), renderChartSelector(window.location.pathname.includes('/toothchart'))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: `/patientprofile/${nationalId}/gallery`,\n                className: ({\n                  isActive\n                }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(FaImages, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"Gallery\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: `/patientprofile/${nationalId}/appointments`,\n                className: ({\n                  isActive\n                }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: `/patientprofile/${nationalId}/consent`,\n                className: ({\n                  isActive\n                }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(FaFileSignature, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"Consent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: `/patientprofile/${nationalId}/reviewsteps`,\n                className: ({\n                  isActive\n                }) => `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"Review Steps\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowLabPopup(true),\n                className: \"flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 text-gray-600 hover:bg-gray-50 hover:text-[#FF6B35]\",\n                children: [/*#__PURE__*/_jsxDEV(FaFlask, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs mt-1 font-medium\",\n                  children: \"Lab\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block w-48\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), showLabPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.9\n        },\n        className: \"bg-white rounded-xl shadow-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FaFlask, {\n              className: \"h-6 w-6 text-[#FF6B35] mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-800\",\n              children: \"Lab Requests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowLabPopup(false);\n              setShowSubmitForm(false);\n              setSelectedLabType('');\n              setNotes('');\n            },\n            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(FaTimes, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), patientData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-700 mb-2\",\n            children: \"Patient Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), \" \", patientData.fullName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"National ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), \" \", patientData.nationalId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), !showSubmitForm ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.02\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 cursor-pointer transition-all duration-200 hover:shadow-lg\",\n            onClick: () => handleLabTypeSelect('university'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaUniversity, {\n                className: \"h-6 w-6 text-blue-600 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-blue-800\",\n                children: \"University Lab\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-700 text-sm\",\n              children: \"Submit request to the university laboratory for dental work and analysis.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.02\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            className: \"bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200 cursor-pointer transition-all duration-200 hover:shadow-lg\",\n            onClick: () => handleLabTypeSelect('outside'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaBuilding, {\n                className: \"h-6 w-6 text-green-600 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-green-800\",\n                children: \"Outside Lab\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-700 text-sm\",\n              children: \"Submit request to an external laboratory for specialized dental services.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"bg-gray-50 p-4 rounded-xl mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: [\"Submit Request - \", selectedLabType === 'university' ? 'University Lab' : 'Outside Lab']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Additional Notes (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: notes,\n              onChange: e => setNotes(e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35] focus:border-[#FF6B35]\",\n              rows: \"3\",\n              placeholder: \"Enter any additional notes or requirements...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmitRequest,\n              disabled: submitting,\n              className: \"px-4 py-2 bg-[#FF6B35] text-white rounded-lg hover:bg-[#E55A2B] transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: submitting ? 'Submitting...' : 'Submit Request'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowSubmitForm(false);\n                setSelectedLabType('');\n                setNotes('');\n              },\n              className: \"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Request History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), labRequests.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-6\",\n            children: \"No lab requests found for this patient.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3 max-h-60 overflow-y-auto\",\n            children: labRequests.map(request => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              className: \"bg-white border border-gray-200 rounded-lg p-3 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [getStatusIcon(request.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium text-gray-800 text-sm\",\n                    children: request.labType === 'university' ? 'University Lab' : 'Outside Lab'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`,\n                  children: request.status.charAt(0).toUpperCase() + request.status.slice(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-600 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Submitted:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 23\n                }, this), \" \", new Date(request.submitDate).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 21\n              }, this), request.notes && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-600 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Notes:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 25\n                }, this), \" \", request.notes]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 23\n              }, this), request.responseNotes && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Response:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 25\n                }, this), \" \", request.responseNotes]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 23\n              }, this)]\n            }, request._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), showConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.9\n        },\n        className: \"bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(FaCheck, {\n              className: \"h-6 w-6 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-2\",\n            children: confirmationMessage.includes('Error') ? 'Error' : 'Success'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 mb-6\",\n            children: confirmationMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowConfirmation(false),\n            className: \"w-full px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005A8B] transition-colors\",\n            children: \"OK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(PatientNav, \"woF9vq+/Fym/bJ1Y2hjI7s1/vzE=\", false, function () {\n  return [useParams];\n});\n_c = PatientNav;\nexport default PatientNav;\nvar _c;\n$RefreshReg$(_c, \"PatientNav\");", "map": {"version": 3, "names": ["NavLink", "useParams", "useState", "useEffect", "axios", "motion", "FaFileAlt", "FaHistory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaImages", "FaCalendarAlt", "FaFileSignature", "FaClipboardCheck", "FaFlask", "FaUniversity", "FaBuilding", "FaTimes", "FaCheck", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PatientNav", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "charts", "_s", "nationalId", "patientData", "setPatientData", "loading", "setLoading", "error", "setError", "showLabPopup", "setShowLabPopup", "selectedLabType", "setSelectedLabType", "showSubmitForm", "setShowSubmitForm", "notes", "setNotes", "submitting", "setSubmitting", "labRequests", "setLabRequests", "showConfirmation", "setShowConfirmation", "confirmationMessage", "setConfirmationMessage", "fetchPatientData", "response", "get", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "console", "status", "message", "fetchLabRequests", "token", "localStorage", "getItem", "headers", "Authorization", "patientRequests", "filter", "req", "patientId", "handleLabTypeSelect", "labType", "handleSubmitRequest", "requestData", "patientName", "fullName", "post", "getStatusColor", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "renderChartSelector", "isActive", "length", "div", "initial", "opacity", "y", "animate", "value", "_id", "onChange", "e", "find", "chart", "target", "map", "title", "Date", "date", "toLocaleDateString", "isLocked", "nav", "transition", "duration", "to", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "window", "location", "pathname", "includes", "onClick", "scale", "exit", "whileHover", "whileTap", "rows", "placeholder", "disabled", "request", "x", "char<PERSON>t", "toUpperCase", "slice", "submitDate", "responseNotes", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/PatientNav.jsx"], "sourcesContent": ["import { NavLink, useParams } from 'react-router-dom';\r\nimport { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { motion } from 'framer-motion';\r\nimport { FaFileAlt, FaHistory, FaTooth, FaImages, FaCalendarAlt, FaFileSignature, FaClipboardCheck, FaFlask, FaUniversity, FaBuilding, FaTimes, FaCheck } from 'react-icons/fa';\r\n\r\nconst PatientNav = ({ selectedChart, setSelectedChart, charts }) => {\r\n  const { nationalId } = useParams();\r\n  const [patientData, setPatientData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [showLabPopup, setShowLabPopup] = useState(false);\r\n  const [selectedLabType, setSelectedLabType] = useState('');\r\n  const [showSubmitForm, setShowSubmitForm] = useState(false);\r\n  const [notes, setNotes] = useState('');\r\n  const [submitting, setSubmitting] = useState(false);\r\n  const [labRequests, setLabRequests] = useState([]);\r\n  const [showConfirmation, setShowConfirmation] = useState(false);\r\n  const [confirmationMessage, setConfirmationMessage] = useState('');\r\n\r\n  useEffect(() => {\r\n    const fetchPatientData = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);\r\n        setPatientData(response.data);\r\n      } catch (err) {\r\n        console.error('Error fetching patient data:', err.response?.status, err.response?.data);\r\n        const message = err.response?.status === 404\r\n          ? 'Patient not found'\r\n          : err.response?.data?.message || 'Failed to load patient data';\r\n        setError(message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchPatientData();\r\n    if (showLabPopup) {\r\n      fetchLabRequests();\r\n    }\r\n  }, [nationalId, showLabPopup]);\r\n\r\n  const fetchLabRequests = async () => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      const response = await axios.get('http://localhost:5000/api/lab-requests/student', {\r\n        headers: { Authorization: `Bearer ${token}` }\r\n      });\r\n      // Filter requests for current patient\r\n      const patientRequests = response.data.filter(req => req.patientId === nationalId);\r\n      setLabRequests(patientRequests);\r\n    } catch (error) {\r\n      console.error('Error fetching lab requests:', error);\r\n    }\r\n  };\r\n\r\n  const handleLabTypeSelect = (labType) => {\r\n    setSelectedLabType(labType);\r\n    setShowSubmitForm(true);\r\n  };\r\n\r\n  const handleSubmitRequest = async () => {\r\n    if (!selectedLabType || !patientData) return;\r\n\r\n    setSubmitting(true);\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      const requestData = {\r\n        patientId: nationalId,\r\n        patientName: patientData.fullName,\r\n        labType: selectedLabType,\r\n        notes: notes\r\n      };\r\n\r\n      await axios.post('http://localhost:5000/api/lab-requests', requestData, {\r\n        headers: { Authorization: `Bearer ${token}` }\r\n      });\r\n\r\n      // Reset form\r\n      setSelectedLabType('');\r\n      setShowSubmitForm(false);\r\n      setNotes('');\r\n\r\n      // Refresh lab requests\r\n      fetchLabRequests();\r\n\r\n      // Show confirmation popup\r\n      setConfirmationMessage('Lab request submitted successfully!');\r\n      setShowConfirmation(true);\r\n    } catch (error) {\r\n      console.error('Error submitting lab request:', error);\r\n      setConfirmationMessage('Error submitting lab request. Please try again.');\r\n      setShowConfirmation(true);\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status) => {\r\n    switch (status) {\r\n      case 'pending': return 'text-orange-600 bg-orange-100';\r\n      case 'approved': return 'text-green-600 bg-green-100';\r\n      case 'rejected': return 'text-red-600 bg-red-100';\r\n      case 'completed': return 'text-blue-600 bg-blue-100';\r\n      default: return 'text-gray-600 bg-gray-100';\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status) => {\r\n    switch (status) {\r\n      case 'approved': return <FaCheck className=\"h-4 w-4\" />;\r\n      case 'rejected': return <FaTimes className=\"h-4 w-4\" />;\r\n      default: return <FaFlask className=\"h-4 w-4\" />;\r\n    }\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"bg-white shadow-sm border-b border-gray-100 px-4 py-3\">\r\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\r\n          <div className=\"animate-pulse flex items-center space-x-4\">\r\n            <div className=\"rounded-full bg-gray-200 h-10 w-10\"></div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"h-4 bg-gray-200 rounded w-32\"></div>\r\n              <div className=\"h-4 bg-gray-200 rounded w-24\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !patientData) {\r\n    return (\r\n      <div className=\"bg-white shadow-sm border-b border-gray-100 px-4 py-3\">\r\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\r\n          <div className=\"text-red-500\">{error || 'Patient not found'}</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderChartSelector = (isActive) => {\r\n    if (!isActive || !charts || charts.length === 0) return null;\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"ml-2\"\r\n      >\r\n        <select\r\n          value={selectedChart?._id || ''}\r\n          onChange={(e) => setSelectedChart(charts.find(chart => chart._id === e.target.value))}\r\n          className=\"px-3 py-1.5 rounded-lg bg-white border border-gray-200 text-sm text-gray-600 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-200\"\r\n        >\r\n          {charts.map(chart => (\r\n            <option key={chart._id} value={chart._id}>\r\n              {chart.title} - {new Date(chart.date).toLocaleDateString()} {chart.isLocked ? '(Locked)' : ''}\r\n            </option>\r\n          ))}\r\n        </select>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <motion.nav\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.3 }}\r\n        className=\"bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)] px-4 py-3 overflow-x-auto w-full\"\r\n      >\r\n      <div className=\"max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center justify-between gap-4\">\r\n        <NavLink to={`/patientprofile/${nationalId}`} className=\"flex items-center w-full md:w-auto hover:opacity-80 transition-opacity\">\r\n          <div className=\"bg-[rgba(0,119,182,0.1)] p-2 rounded-lg mr-3 flex-shrink-0\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className=\"h-6 w-6 text-[#0077B6]\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <div className=\"min-w-0\">\r\n            <h2 className=\"text-xs font-medium text-gray-500 uppercase tracking-wider\">Current Patient</h2>\r\n            <h1 className=\"text-lg font-semibold text-gray-800 truncate\">{patientData.fullName}</h1>\r\n          </div>\r\n        </NavLink>\r\n\r\n        <div className=\"w-full md:w-auto mx-auto flex flex-col md:flex-row items-center gap-2\">\r\n          <ul className=\"flex flex-wrap justify-center gap-1 md:gap-2 w-full overflow-x-auto py-2\">\r\n\r\n\r\n            {/* Sheets */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/sheets`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaFileAlt className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Patient Examination Sheet</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* History */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/history`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaHistory className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">History</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* Tooth Chart */}\r\n            <li className=\"flex items-center\">\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/toothchart`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaTooth className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Tooth Chart</span>\r\n              </NavLink>\r\n              {renderChartSelector(window.location.pathname.includes('/toothchart'))}\r\n            </li>\r\n\r\n\r\n\r\n            {/* Gallery */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/gallery`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaImages className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Gallery</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n\r\n\r\n            {/* Appointments */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/appointments`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaCalendarAlt className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Appointments</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* Consent */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/consent`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaFileSignature className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Consent</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* Review Steps */}\r\n            <li>\r\n              <NavLink\r\n                to={`/patientprofile/${nationalId}/reviewsteps`}\r\n                className={({ isActive }) =>\r\n                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${\r\n                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'\r\n                  }`\r\n                }\r\n              >\r\n                <FaClipboardCheck className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Review Steps</span>\r\n              </NavLink>\r\n            </li>\r\n\r\n            {/* Lab */}\r\n            <li>\r\n              <button\r\n                onClick={() => setShowLabPopup(true)}\r\n                className=\"flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 text-gray-600 hover:bg-gray-50 hover:text-[#FF6B35]\"\r\n              >\r\n                <FaFlask className=\"h-5 w-5\" />\r\n                <span className=\"text-xs mt-1 font-medium\">Lab</span>\r\n              </button>\r\n            </li>\r\n          </ul>\r\n\r\n        </div>\r\n\r\n        <div className=\"hidden md:block w-48\"></div>\r\n      </div>\r\n      </motion.nav>\r\n\r\n      {/* Lab Popup */}\r\n    {showLabPopup && (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.9 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          exit={{ opacity: 0, scale: 0.9 }}\r\n          className=\"bg-white rounded-xl shadow-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\"\r\n        >\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <div className=\"flex items-center\">\r\n              <FaFlask className=\"h-6 w-6 text-[#FF6B35] mr-3\" />\r\n              <h2 className=\"text-2xl font-bold text-gray-800\">Lab Requests</h2>\r\n            </div>\r\n            <button\r\n              onClick={() => {\r\n                setShowLabPopup(false);\r\n                setShowSubmitForm(false);\r\n                setSelectedLabType('');\r\n                setNotes('');\r\n              }}\r\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\r\n            >\r\n              <FaTimes className=\"h-6 w-6\" />\r\n            </button>\r\n          </div>\r\n\r\n          {patientData && (\r\n            <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n              <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">Patient Information</h3>\r\n              <p className=\"text-gray-600\">\r\n                <span className=\"font-medium\">Name:</span> {patientData.fullName}\r\n              </p>\r\n              <p className=\"text-gray-600\">\r\n                <span className=\"font-medium\">National ID:</span> {patientData.nationalId}\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          {!showSubmitForm ? (\r\n            <div className=\"grid md:grid-cols-2 gap-4 mb-6\">\r\n              <motion.div\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 cursor-pointer transition-all duration-200 hover:shadow-lg\"\r\n                onClick={() => handleLabTypeSelect('university')}\r\n              >\r\n                <div className=\"flex items-center mb-3\">\r\n                  <FaUniversity className=\"h-6 w-6 text-blue-600 mr-3\" />\r\n                  <h3 className=\"text-lg font-semibold text-blue-800\">University Lab</h3>\r\n                </div>\r\n                <p className=\"text-blue-700 text-sm\">\r\n                  Submit request to the university laboratory for dental work and analysis.\r\n                </p>\r\n              </motion.div>\r\n\r\n              <motion.div\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                className=\"bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200 cursor-pointer transition-all duration-200 hover:shadow-lg\"\r\n                onClick={() => handleLabTypeSelect('outside')}\r\n              >\r\n                <div className=\"flex items-center mb-3\">\r\n                  <FaBuilding className=\"h-6 w-6 text-green-600 mr-3\" />\r\n                  <h3 className=\"text-lg font-semibold text-green-800\">Outside Lab</h3>\r\n                </div>\r\n                <p className=\"text-green-700 text-sm\">\r\n                  Submit request to an external laboratory for specialized dental services.\r\n                </p>\r\n              </motion.div>\r\n            </div>\r\n          ) : (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"bg-gray-50 p-4 rounded-xl mb-6\"\r\n            >\r\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">\r\n                Submit Request - {selectedLabType === 'university' ? 'University Lab' : 'Outside Lab'}\r\n              </h3>\r\n\r\n              <div className=\"mb-4\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Additional Notes (Optional)\r\n                </label>\r\n                <textarea\r\n                  value={notes}\r\n                  onChange={(e) => setNotes(e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35] focus:border-[#FF6B35]\"\r\n                  rows=\"3\"\r\n                  placeholder=\"Enter any additional notes or requirements...\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex gap-3\">\r\n                <button\r\n                  onClick={handleSubmitRequest}\r\n                  disabled={submitting}\r\n                  className=\"px-4 py-2 bg-[#FF6B35] text-white rounded-lg hover:bg-[#E55A2B] transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {submitting ? 'Submitting...' : 'Submit Request'}\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    setShowSubmitForm(false);\r\n                    setSelectedLabType('');\r\n                    setNotes('');\r\n                  }}\r\n                  className=\"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n\r\n          {/* Lab Requests History */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Request History</h3>\r\n            {labRequests.length === 0 ? (\r\n              <p className=\"text-gray-500 text-center py-6\">No lab requests found for this patient.</p>\r\n            ) : (\r\n              <div className=\"space-y-3 max-h-60 overflow-y-auto\">\r\n                {labRequests.map((request) => (\r\n                  <motion.div\r\n                    key={request._id}\r\n                    initial={{ opacity: 0, x: -20 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    className=\"bg-white border border-gray-200 rounded-lg p-3 shadow-sm\"\r\n                  >\r\n                    <div className=\"flex items-center justify-between mb-2\">\r\n                      <div className=\"flex items-center\">\r\n                        {getStatusIcon(request.status)}\r\n                        <span className=\"ml-2 font-medium text-gray-800 text-sm\">\r\n                          {request.labType === 'university' ? 'University Lab' : 'Outside Lab'}\r\n                        </span>\r\n                      </div>\r\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>\r\n                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}\r\n                      </span>\r\n                    </div>\r\n                    <p className=\"text-xs text-gray-600 mb-1\">\r\n                      <span className=\"font-medium\">Submitted:</span> {new Date(request.submitDate).toLocaleDateString()}\r\n                    </p>\r\n                    {request.notes && (\r\n                      <p className=\"text-xs text-gray-600 mb-1\">\r\n                        <span className=\"font-medium\">Notes:</span> {request.notes}\r\n                      </p>\r\n                    )}\r\n                    {request.responseNotes && (\r\n                      <p className=\"text-xs text-gray-600\">\r\n                        <span className=\"font-medium\">Response:</span> {request.responseNotes}\r\n                      </p>\r\n                    )}\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n      )}\r\n\r\n      {/* Confirmation Popup */}\r\n      {showConfirmation && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            exit={{ opacity: 0, scale: 0.9 }}\r\n            className=\"bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4\"\r\n          >\r\n            <div className=\"text-center\">\r\n              <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4\">\r\n                <FaCheck className=\"h-6 w-6 text-green-600\" />\r\n              </div>\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\r\n                {confirmationMessage.includes('Error') ? 'Error' : 'Success'}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-500 mb-6\">\r\n                {confirmationMessage}\r\n              </p>\r\n              <button\r\n                onClick={() => setShowConfirmation(false)}\r\n                className=\"w-full px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005A8B] transition-colors\"\r\n              >\r\n                OK\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PatientNav;"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,SAAS,QAAQ,kBAAkB;AACrD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,YAAY,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhL,MAAMC,UAAU,GAAGA,CAAC;EAAEC,aAAa;EAAEC,gBAAgB;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM;IAAEC;EAAW,CAAC,GAAG1B,SAAS,CAAC,CAAC;EAClC,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAElEC,SAAS,CAAC,MAAM;IACd,MAAM+C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM/C,KAAK,CAACgD,GAAG,CAAC,6CAA6CzB,UAAU,EAAE,CAAC;QAC3FE,cAAc,CAACsB,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZC,OAAO,CAAC5B,KAAK,CAAC,8BAA8B,GAAAuB,aAAA,GAAED,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcM,MAAM,GAAAL,cAAA,GAAEF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcH,IAAI,CAAC;QACvF,MAAMS,OAAO,GAAG,EAAAL,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcI,MAAM,MAAK,GAAG,GACxC,mBAAmB,GACnB,EAAAH,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,6BAA6B;QAChE7B,QAAQ,CAAC6B,OAAO,CAAC;MACnB,CAAC,SAAS;QACR/B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDmB,gBAAgB,CAAC,CAAC;IAClB,IAAIhB,YAAY,EAAE;MAChB6B,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACpC,UAAU,EAAEO,YAAY,CAAC,CAAC;EAE9B,MAAM6B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMf,QAAQ,GAAG,MAAM/C,KAAK,CAACgD,GAAG,CAAC,gDAAgD,EAAE;QACjFe,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAC9C,CAAC,CAAC;MACF;MACA,MAAMK,eAAe,GAAGlB,QAAQ,CAACE,IAAI,CAACiB,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,KAAK7C,UAAU,CAAC;MACjFkB,cAAc,CAACwB,eAAe,CAAC;IACjC,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMyC,mBAAmB,GAAIC,OAAO,IAAK;IACvCrC,kBAAkB,CAACqC,OAAO,CAAC;IAC3BnC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACvC,eAAe,IAAI,CAACR,WAAW,EAAE;IAEtCe,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMU,WAAW,GAAG;QAClBJ,SAAS,EAAE7C,UAAU;QACrBkD,WAAW,EAAEjD,WAAW,CAACkD,QAAQ;QACjCJ,OAAO,EAAEtC,eAAe;QACxBI,KAAK,EAAEA;MACT,CAAC;MAED,MAAMpC,KAAK,CAAC2E,IAAI,CAAC,wCAAwC,EAAEH,WAAW,EAAE;QACtET,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAC9C,CAAC,CAAC;;MAEF;MACA3B,kBAAkB,CAAC,EAAE,CAAC;MACtBE,iBAAiB,CAAC,KAAK,CAAC;MACxBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAsB,gBAAgB,CAAC,CAAC;;MAElB;MACAd,sBAAsB,CAAC,qCAAqC,CAAC;MAC7DF,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDiB,sBAAsB,CAAC,iDAAiD,CAAC;MACzEF,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,SAAS;MACRJ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqC,cAAc,GAAInB,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,UAAU;QAAE,OAAO,6BAA6B;MACrD,KAAK,UAAU;QAAE,OAAO,yBAAyB;MACjD,KAAK,WAAW;QAAE,OAAO,2BAA2B;MACpD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMoB,aAAa,GAAIpB,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,oBAAO1C,OAAA,CAACF,OAAO;UAACiE,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,UAAU;QAAE,oBAAOnE,OAAA,CAACH,OAAO;UAACkE,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD;QAAS,oBAAOnE,OAAA,CAACN,OAAO;UAACqE,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACjD;EACF,CAAC;EAID,IAAIxD,OAAO,EAAE;IACX,oBACEX,OAAA;MAAK+D,SAAS,EAAC,uDAAuD;MAAAK,QAAA,eACpEpE,OAAA;QAAK+D,SAAS,EAAC,qDAAqD;QAAAK,QAAA,eAClEpE,OAAA;UAAK+D,SAAS,EAAC,2CAA2C;UAAAK,QAAA,gBACxDpE,OAAA;YAAK+D,SAAS,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DnE,OAAA;YAAK+D,SAAS,EAAC,WAAW;YAAAK,QAAA,gBACxBpE,OAAA;cAAK+D,SAAS,EAAC;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDnE,OAAA;cAAK+D,SAAS,EAAC;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItD,KAAK,IAAI,CAACJ,WAAW,EAAE;IACzB,oBACET,OAAA;MAAK+D,SAAS,EAAC,uDAAuD;MAAAK,QAAA,eACpEpE,OAAA;QAAK+D,SAAS,EAAC,qDAAqD;QAAAK,QAAA,eAClEpE,OAAA;UAAK+D,SAAS,EAAC,cAAc;UAAAK,QAAA,EAAEvD,KAAK,IAAI;QAAmB;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,IAAI,CAACA,QAAQ,IAAI,CAAChE,MAAM,IAAIA,MAAM,CAACiE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC5D,oBACEvE,OAAA,CAACd,MAAM,CAACsF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BZ,SAAS,EAAC,MAAM;MAAAK,QAAA,eAEhBpE,OAAA;QACE6E,KAAK,EAAE,CAAAzE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0E,GAAG,KAAI,EAAG;QAChCC,QAAQ,EAAGC,CAAC,IAAK3E,gBAAgB,CAACC,MAAM,CAAC2E,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACJ,GAAG,KAAKE,CAAC,CAACG,MAAM,CAACN,KAAK,CAAC,CAAE;QACtFd,SAAS,EAAC,sLAAsL;QAAAK,QAAA,EAE/L9D,MAAM,CAAC8E,GAAG,CAACF,KAAK,iBACflF,OAAA;UAAwB6E,KAAK,EAAEK,KAAK,CAACJ,GAAI;UAAAV,QAAA,GACtCc,KAAK,CAACG,KAAK,EAAC,KAAG,EAAC,IAAIC,IAAI,CAACJ,KAAK,CAACK,IAAI,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,GAAC,EAACN,KAAK,CAACO,QAAQ,GAAG,UAAU,GAAG,EAAE;QAAA,GADlFP,KAAK,CAACJ,GAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEd,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEjB,CAAC;EAED,oBACEnE,OAAA,CAAAE,SAAA;IAAAkE,QAAA,gBACEpE,OAAA,CAACd,MAAM,CAACwG,GAAG;MACTjB,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BgB,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9B7B,SAAS,EAAC,2FAA2F;MAAAK,QAAA,eAEvGpE,OAAA;QAAK+D,SAAS,EAAC,+FAA+F;QAAAK,QAAA,gBAC5GpE,OAAA,CAACnB,OAAO;UAACgH,EAAE,EAAE,mBAAmBrF,UAAU,EAAG;UAACuD,SAAS,EAAC,wEAAwE;UAAAK,QAAA,gBAC9HpE,OAAA;YAAK+D,SAAS,EAAC,4DAA4D;YAAAK,QAAA,eACzEpE,OAAA;cACE8F,KAAK,EAAC,4BAA4B;cAClC/B,SAAS,EAAC,wBAAwB;cAClCgC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAA7B,QAAA,eAErBpE,OAAA;gBACEkG,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAAqE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnE,OAAA;YAAK+D,SAAS,EAAC,SAAS;YAAAK,QAAA,gBACtBpE,OAAA;cAAI+D,SAAS,EAAC,4DAA4D;cAAAK,QAAA,EAAC;YAAe;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/FnE,OAAA;cAAI+D,SAAS,EAAC,8CAA8C;cAAAK,QAAA,EAAE3D,WAAW,CAACkD;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEVnE,OAAA;UAAK+D,SAAS,EAAC,uEAAuE;UAAAK,QAAA,eACpFpE,OAAA;YAAI+D,SAAS,EAAC,0EAA0E;YAAAK,QAAA,gBAItFpE,OAAA;cAAAoE,QAAA,eACEpE,OAAA,CAACnB,OAAO;gBACNgH,EAAE,EAAE,mBAAmBrF,UAAU,SAAU;gBAC3CuD,SAAS,EAAEA,CAAC;kBAAEO;gBAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;gBAAAF,QAAA,gBAEDpE,OAAA,CAACb,SAAS;kBAAC4E,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAyB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGLnE,OAAA;cAAAoE,QAAA,eACEpE,OAAA,CAACnB,OAAO;gBACNgH,EAAE,EAAE,mBAAmBrF,UAAU,UAAW;gBAC5CuD,SAAS,EAAEA,CAAC;kBAAEO;gBAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;gBAAAF,QAAA,gBAEDpE,OAAA,CAACZ,SAAS;kBAAC2E,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGLnE,OAAA;cAAI+D,SAAS,EAAC,mBAAmB;cAAAK,QAAA,gBAC/BpE,OAAA,CAACnB,OAAO;gBACNgH,EAAE,EAAE,mBAAmBrF,UAAU,aAAc;gBAC/CuD,SAAS,EAAEA,CAAC;kBAAEO;gBAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;gBAAAF,QAAA,gBAEDpE,OAAA,CAACX,OAAO;kBAAC0E,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/BnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAW;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,EACTE,mBAAmB,CAACiC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eAKLnE,OAAA;cAAAoE,QAAA,eACEpE,OAAA,CAACnB,OAAO;gBACNgH,EAAE,EAAE,mBAAmBrF,UAAU,UAAW;gBAC5CuD,SAAS,EAAEA,CAAC;kBAAEO;gBAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;gBAAAF,QAAA,gBAEDpE,OAAA,CAACV,QAAQ;kBAACyE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChCnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAKLnE,OAAA;cAAAoE,QAAA,eACEpE,OAAA,CAACnB,OAAO;gBACNgH,EAAE,EAAE,mBAAmBrF,UAAU,eAAgB;gBACjDuD,SAAS,EAAEA,CAAC;kBAAEO;gBAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;gBAAAF,QAAA,gBAEDpE,OAAA,CAACT,aAAa;kBAACwE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAY;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGLnE,OAAA;cAAAoE,QAAA,eACEpE,OAAA,CAACnB,OAAO;gBACNgH,EAAE,EAAE,mBAAmBrF,UAAU,UAAW;gBAC5CuD,SAAS,EAAEA,CAAC;kBAAEO;gBAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;gBAAAF,QAAA,gBAEDpE,OAAA,CAACR,eAAe;kBAACuE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGLnE,OAAA;cAAAoE,QAAA,eACEpE,OAAA,CAACnB,OAAO;gBACNgH,EAAE,EAAE,mBAAmBrF,UAAU,cAAe;gBAChDuD,SAAS,EAAEA,CAAC;kBAAEO;gBAAS,CAAC,KACtB,+EACEA,QAAQ,GAAG,gCAAgC,GAAG,gCAAgC,EAEjF;gBAAAF,QAAA,gBAEDpE,OAAA,CAACP,gBAAgB;kBAACsE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxCnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAY;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGLnE,OAAA;cAAAoE,QAAA,eACEpE,OAAA;gBACE0G,OAAO,EAAEA,CAAA,KAAM1F,eAAe,CAAC,IAAI,CAAE;gBACrC+C,SAAS,EAAC,iIAAiI;gBAAAK,QAAA,gBAE3IpE,OAAA,CAACN,OAAO;kBAACqE,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/BnE,OAAA;kBAAM+D,SAAS,EAAC,0BAA0B;kBAAAK,QAAA,EAAC;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEF,CAAC,eAENnE,OAAA;UAAK+D,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAGdpD,YAAY,iBACXf,OAAA;MAAK+D,SAAS,EAAC,4EAA4E;MAAAK,QAAA,eACzFpE,OAAA,CAACd,MAAM,CAACsF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEiC,KAAK,EAAE;QAAI,CAAE;QACpC/B,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEiC,KAAK,EAAE;QAAE,CAAE;QAClCC,IAAI,EAAE;UAAElC,OAAO,EAAE,CAAC;UAAEiC,KAAK,EAAE;QAAI,CAAE;QACjC5C,SAAS,EAAC,uFAAuF;QAAAK,QAAA,gBAEjGpE,OAAA;UAAK+D,SAAS,EAAC,wCAAwC;UAAAK,QAAA,gBACrDpE,OAAA;YAAK+D,SAAS,EAAC,mBAAmB;YAAAK,QAAA,gBAChCpE,OAAA,CAACN,OAAO;cAACqE,SAAS,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDnE,OAAA;cAAI+D,SAAS,EAAC,kCAAkC;cAAAK,QAAA,EAAC;YAAY;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNnE,OAAA;YACE0G,OAAO,EAAEA,CAAA,KAAM;cACb1F,eAAe,CAAC,KAAK,CAAC;cACtBI,iBAAiB,CAAC,KAAK,CAAC;cACxBF,kBAAkB,CAAC,EAAE,CAAC;cACtBI,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFyC,SAAS,EAAC,qDAAqD;YAAAK,QAAA,eAE/DpE,OAAA,CAACH,OAAO;cAACkE,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL1D,WAAW,iBACVT,OAAA;UAAK+D,SAAS,EAAC,gCAAgC;UAAAK,QAAA,gBAC7CpE,OAAA;YAAI+D,SAAS,EAAC,0CAA0C;YAAAK,QAAA,EAAC;UAAmB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFnE,OAAA;YAAG+D,SAAS,EAAC,eAAe;YAAAK,QAAA,gBAC1BpE,OAAA;cAAM+D,SAAS,EAAC,aAAa;cAAAK,QAAA,EAAC;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAAC1D,WAAW,CAACkD,QAAQ;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACJnE,OAAA;YAAG+D,SAAS,EAAC,eAAe;YAAAK,QAAA,gBAC1BpE,OAAA;cAAM+D,SAAS,EAAC,aAAa;cAAAK,QAAA,EAAC;YAAY;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAAC1D,WAAW,CAACD,UAAU;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEA,CAAChD,cAAc,gBACdnB,OAAA;UAAK+D,SAAS,EAAC,gCAAgC;UAAAK,QAAA,gBAC7CpE,OAAA,CAACd,MAAM,CAACsF,GAAG;YACTqC,UAAU,EAAE;cAAEF,KAAK,EAAE;YAAK,CAAE;YAC5BG,QAAQ,EAAE;cAAEH,KAAK,EAAE;YAAK,CAAE;YAC1B5C,SAAS,EAAC,6IAA6I;YACvJ2C,OAAO,EAAEA,CAAA,KAAMpD,mBAAmB,CAAC,YAAY,CAAE;YAAAc,QAAA,gBAEjDpE,OAAA;cAAK+D,SAAS,EAAC,wBAAwB;cAAAK,QAAA,gBACrCpE,OAAA,CAACL,YAAY;gBAACoE,SAAS,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDnE,OAAA;gBAAI+D,SAAS,EAAC,qCAAqC;gBAAAK,QAAA,EAAC;cAAc;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACNnE,OAAA;cAAG+D,SAAS,EAAC,uBAAuB;cAAAK,QAAA,EAAC;YAErC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEbnE,OAAA,CAACd,MAAM,CAACsF,GAAG;YACTqC,UAAU,EAAE;cAAEF,KAAK,EAAE;YAAK,CAAE;YAC5BG,QAAQ,EAAE;cAAEH,KAAK,EAAE;YAAK,CAAE;YAC1B5C,SAAS,EAAC,gJAAgJ;YAC1J2C,OAAO,EAAEA,CAAA,KAAMpD,mBAAmB,CAAC,SAAS,CAAE;YAAAc,QAAA,gBAE9CpE,OAAA;cAAK+D,SAAS,EAAC,wBAAwB;cAAAK,QAAA,gBACrCpE,OAAA,CAACJ,UAAU;gBAACmE,SAAS,EAAC;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDnE,OAAA;gBAAI+D,SAAS,EAAC,sCAAsC;gBAAAK,QAAA,EAAC;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNnE,OAAA;cAAG+D,SAAS,EAAC,wBAAwB;cAAAK,QAAA,EAAC;YAEtC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAENnE,OAAA,CAACd,MAAM,CAACsF,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BZ,SAAS,EAAC,gCAAgC;UAAAK,QAAA,gBAE1CpE,OAAA;YAAI+D,SAAS,EAAC,0CAA0C;YAAAK,QAAA,GAAC,mBACtC,EAACnD,eAAe,KAAK,YAAY,GAAG,gBAAgB,GAAG,aAAa;UAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAELnE,OAAA;YAAK+D,SAAS,EAAC,MAAM;YAAAK,QAAA,gBACnBpE,OAAA;cAAO+D,SAAS,EAAC,8CAA8C;cAAAK,QAAA,EAAC;YAEhE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cACE6E,KAAK,EAAExD,KAAM;cACb0D,QAAQ,EAAGC,CAAC,IAAK1D,QAAQ,CAAC0D,CAAC,CAACG,MAAM,CAACN,KAAK,CAAE;cAC1Cd,SAAS,EAAC,gIAAgI;cAC1IgD,IAAI,EAAC,GAAG;cACRC,WAAW,EAAC;YAA+C;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnE,OAAA;YAAK+D,SAAS,EAAC,YAAY;YAAAK,QAAA,gBACzBpE,OAAA;cACE0G,OAAO,EAAElD,mBAAoB;cAC7ByD,QAAQ,EAAE1F,UAAW;cACrBwC,SAAS,EAAC,mIAAmI;cAAAK,QAAA,EAE5I7C,UAAU,GAAG,eAAe,GAAG;YAAgB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACTnE,OAAA;cACE0G,OAAO,EAAEA,CAAA,KAAM;gBACbtF,iBAAiB,CAAC,KAAK,CAAC;gBACxBF,kBAAkB,CAAC,EAAE,CAAC;gBACtBI,QAAQ,CAAC,EAAE,CAAC;cACd,CAAE;cACFyC,SAAS,EAAC,iFAAiF;cAAAK,QAAA,EAC5F;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGDnE,OAAA;UAAAoE,QAAA,gBACEpE,OAAA;YAAI+D,SAAS,EAAC,0CAA0C;YAAAK,QAAA,EAAC;UAAe;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC5E1C,WAAW,CAAC8C,MAAM,KAAK,CAAC,gBACvBvE,OAAA;YAAG+D,SAAS,EAAC,gCAAgC;YAAAK,QAAA,EAAC;UAAuC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAEzFnE,OAAA;YAAK+D,SAAS,EAAC,oCAAoC;YAAAK,QAAA,EAChD3C,WAAW,CAAC2D,GAAG,CAAE8B,OAAO,iBACvBlH,OAAA,CAACd,MAAM,CAACsF,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEyC,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCvC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEyC,CAAC,EAAE;cAAE,CAAE;cAC9BpD,SAAS,EAAC,0DAA0D;cAAAK,QAAA,gBAEpEpE,OAAA;gBAAK+D,SAAS,EAAC,wCAAwC;gBAAAK,QAAA,gBACrDpE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAK,QAAA,GAC/BN,aAAa,CAACoD,OAAO,CAACxE,MAAM,CAAC,eAC9B1C,OAAA;oBAAM+D,SAAS,EAAC,wCAAwC;oBAAAK,QAAA,EACrD8C,OAAO,CAAC3D,OAAO,KAAK,YAAY,GAAG,gBAAgB,GAAG;kBAAa;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNnE,OAAA;kBAAM+D,SAAS,EAAE,8CAA8CF,cAAc,CAACqD,OAAO,CAACxE,MAAM,CAAC,EAAG;kBAAA0B,QAAA,EAC7F8C,OAAO,CAACxE,MAAM,CAAC0E,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,OAAO,CAACxE,MAAM,CAAC4E,KAAK,CAAC,CAAC;gBAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNnE,OAAA;gBAAG+D,SAAS,EAAC,4BAA4B;gBAAAK,QAAA,gBACvCpE,OAAA;kBAAM+D,SAAS,EAAC,aAAa;kBAAAK,QAAA,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC,IAAImB,IAAI,CAAC4B,OAAO,CAACK,UAAU,CAAC,CAAC/B,kBAAkB,CAAC,CAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC,EACH+C,OAAO,CAAC7F,KAAK,iBACZrB,OAAA;gBAAG+D,SAAS,EAAC,4BAA4B;gBAAAK,QAAA,gBACvCpE,OAAA;kBAAM+D,SAAS,EAAC,aAAa;kBAAAK,QAAA,EAAC;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC+C,OAAO,CAAC7F,KAAK;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CACJ,EACA+C,OAAO,CAACM,aAAa,iBACpBxH,OAAA;gBAAG+D,SAAS,EAAC,uBAAuB;gBAAAK,QAAA,gBAClCpE,OAAA;kBAAM+D,SAAS,EAAC,aAAa;kBAAAK,QAAA,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC+C,OAAO,CAACM,aAAa;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA,GA5BI+C,OAAO,CAACpC,GAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BN,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACJ,EAGAxC,gBAAgB,iBACf3B,OAAA;MAAK+D,SAAS,EAAC,4EAA4E;MAAAK,QAAA,eACzFpE,OAAA,CAACd,MAAM,CAACsF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEiC,KAAK,EAAE;QAAI,CAAE;QACpC/B,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEiC,KAAK,EAAE;QAAE,CAAE;QAClCC,IAAI,EAAE;UAAElC,OAAO,EAAE,CAAC;UAAEiC,KAAK,EAAE;QAAI,CAAE;QACjC5C,SAAS,EAAC,yDAAyD;QAAAK,QAAA,eAEnEpE,OAAA;UAAK+D,SAAS,EAAC,aAAa;UAAAK,QAAA,gBAC1BpE,OAAA;YAAK+D,SAAS,EAAC,mFAAmF;YAAAK,QAAA,eAChGpE,OAAA,CAACF,OAAO;cAACiE,SAAS,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNnE,OAAA;YAAI+D,SAAS,EAAC,wCAAwC;YAAAK,QAAA,EACnDvC,mBAAmB,CAAC4E,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG;UAAS;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACLnE,OAAA;YAAG+D,SAAS,EAAC,4BAA4B;YAAAK,QAAA,EACtCvC;UAAmB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJnE,OAAA;YACE0G,OAAO,EAAEA,CAAA,KAAM9E,mBAAmB,CAAC,KAAK,CAAE;YAC1CmC,SAAS,EAAC,0FAA0F;YAAAK,QAAA,EACrG;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAC5D,EAAA,CAvgBIJ,UAAU;EAAA,QACSrB,SAAS;AAAA;AAAA2I,EAAA,GAD5BtH,UAAU;AAygBhB,eAAeA,UAAU;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}