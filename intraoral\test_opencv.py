#!/usr/bin/env python3
"""
Test script to verify OpenCV installation
"""
import cv2
import numpy as np

def test_opencv():
    try:
        # Test basic OpenCV functionality
        print("Testing OpenCV installation...")
        
        # Create a test image
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:] = (255, 0, 0)  # Blue color
        
        # Test drawing functions
        cv2.rectangle(test_image, (10, 10), (90, 90), (0, 255, 0), 2)
        cv2.putText(test_image, "OpenCV OK", (15, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Test saving
        cv2.imwrite("test_opencv.jpg", test_image)
        
        print("✅ OpenCV is working correctly!")
        print("✅ Test image saved as 'test_opencv.jpg'")
        return True
        
    except Exception as e:
        print(f"❌ OpenCV test failed: {e}")
        return False

if __name__ == "__main__":
    test_opencv() 