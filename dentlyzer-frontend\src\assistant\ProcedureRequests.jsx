import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AssistantSidebar from './AssistantSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import {
  FaCheckCircle,
  FaTimesCircle,
  FaSearch,
  FaFilter,
  FaClipboardList,
  FaExclamationTriangle,
  FaInfoCircle,
} from 'react-icons/fa';

const ProcedureRequests = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [procedureRequests, setProcedureRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [responseData, setResponseData] = useState({
    status: 'approved',
    responseNotes: '',
  });
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchProcedureRequests = async () => {
      if (!user || !token) {
        setError('Please log in to view procedure requests.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/procedure-requests`, config);

        if (Array.isArray(response.data)) {
          setProcedureRequests(response.data);
        } else {
          setError('Invalid data received from server');
          setProcedureRequests([]);
        }
      } catch (err) {
        console.error('Fetch error:', err.response?.status, err.response?.data);
        setError(err.response?.data?.message || 'Failed to load procedure requests');
        setProcedureRequests([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProcedureRequests();
  }, [user, token]);

  const handleSearch = (e) => setSearchTerm(e.target.value);
  const handleStatusFilterChange = (e) => setStatusFilter(e.target.value);

  const filteredRequests = procedureRequests.filter(request => {
    // Apply status filter
    if (statusFilter !== 'all' && request.status !== statusFilter) {
      return false;
    }

    // Apply search filter
    const searchTermLower = searchTerm.toLowerCase();
    return (
      request.patientName?.toLowerCase().includes(searchTermLower) ||
      request.patientNationalId?.includes(searchTerm) ||
      request.studentName?.toLowerCase().includes(searchTermLower) ||
      request.procedureType?.toLowerCase().includes(searchTermLower)
    );
  });

  const handleResponseSubmit = async (e) => {
    e.preventDefault();
    if (!selectedRequest) return;

    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/procedure-requests/${selectedRequest._id}`,
        responseData,
        config
      );

      if (response.data) {
        // Update the procedure request in the list
        setProcedureRequests(prevRequests =>
          prevRequests.map(req =>
            req._id === selectedRequest._id ? response.data.procedureRequest : req
          )
        );

        setShowResponseModal(false);
        setSelectedRequest(null);
        setResponseData({
          status: 'approved',
          responseNotes: '',
        });
      }
    } catch (err) {
      console.error('Response error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to respond to procedure request');
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <FaExclamationTriangle className="w-5 h-5 text-red-500 mr-3" />
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Procedure Requests
                  </h1>
                  <p className="text-[#333333]">Manage student procedure requests</p>
                </div>
                <div className="flex flex-col md:flex-row items-center gap-4 w-full md:w-auto">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="relative w-full md:w-64"
                  >
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaSearch className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search requests..."
                      value={searchTerm}
                      onChange={handleSearch}
                      className="block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-lg bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:bg-white sm:text-sm transition-all duration-200"
                    />
                  </motion.div>
                  <select
                    value={statusFilter}
                    onChange={handleStatusFilterChange}
                    className="w-full md:w-auto px-4 py-2.5 border border-gray-200 rounded-lg bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-[#20B2AA] sm:text-sm transition-all duration-200"
                  >
                    <option value="all">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-4 md:p-6"
              >
                {filteredRequests.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-[rgba(0,119,182,0.05)]">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Student</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Patient</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Procedure</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#333333] uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredRequests.map((request) => (
                          <tr
                            key={request._id}
                            className="hover:bg-[rgba(0,119,182,0.05)] cursor-pointer"
                            onClick={() => {
                              setSelectedRequest(request);
                              if (request.status === 'pending') {
                                setShowResponseModal(true);
                              }
                            }}
                          >
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {new Date(request.requestDate).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{request.studentName}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {request.patientName ?
                                (request.patientNationalId ?
                                  `${request.patientName} (${request.patientNationalId})` :
                                  request.patientName) :
                                'No patient specified'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{request.procedureType}</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(request.status)}`}>
                                {request.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              {request.status === 'pending' && (
                                <div className="flex justify-end space-x-2">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedRequest(request);
                                      setResponseData({
                                        status: 'approved',
                                        responseNotes: '',
                                      });
                                      setShowResponseModal(true);
                                    }}
                                    className="text-green-600 hover:text-green-800"
                                    title="Approve Request"
                                  >
                                    <FaCheckCircle className="inline h-5 w-5" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedRequest(request);
                                      setResponseData({
                                        status: 'rejected',
                                        responseNotes: '',
                                      });
                                      setShowResponseModal(true);
                                    }}
                                    className="text-red-600 hover:text-red-800"
                                    title="Reject Request"
                                  >
                                    <FaTimesCircle className="inline h-5 w-5" />
                                  </button>
                                </div>
                              )}
                              {request.status !== 'pending' && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedRequest(request);
                                  }}
                                  className="text-blue-600 hover:text-blue-800"
                                  title="View Details"
                                >
                                  <FaInfoCircle className="inline h-5 w-5" />
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FaClipboardList className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900">No procedure requests found</h3>
                    <p className="mt-2 text-sm text-gray-500">
                      {searchTerm || statusFilter !== 'all'
                        ? 'Try adjusting your search or filter criteria'
                        : 'There are no procedure requests at this time'}
                    </p>
                  </div>
                )}
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* Response Modal */}
      {showResponseModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-[#0077B6]">
                {responseData.status === 'approved' ? 'Approve' : 'Reject'} Request
              </h2>
              <button
                onClick={() => setShowResponseModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleResponseSubmit}>
              <div className="mb-6">
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Student:</span> {selectedRequest.studentName}
                  </p>
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Patient:</span> {
                      selectedRequest.patientName ?
                        (selectedRequest.patientNationalId ?
                          `${selectedRequest.patientName} (${selectedRequest.patientNationalId})` :
                          selectedRequest.patientName) :
                        'No patient specified'
                    }
                  </p>
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Procedure:</span> {selectedRequest.procedureType}
                  </p>
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Notes:</span> {selectedRequest.notes || 'None'}
                  </p>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Response Notes</label>
                  <textarea
                    name="responseNotes"
                    value={responseData.responseNotes}
                    onChange={(e) => setResponseData({ ...responseData, responseNotes: e.target.value })}
                    rows="4"
                    placeholder="Provide any additional information about your decision"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <motion.button
                  type="button"
                  onClick={() => setShowResponseModal(false)}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  type="submit"
                  className={`px-6 py-2 text-white rounded-full font-medium transition-colors shadow-md ${
                    responseData.status === 'approved'
                      ? 'bg-gradient-to-r from-green-500 to-green-700 hover:from-green-600 hover:to-green-800'
                      : 'bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {responseData.status === 'approved' ? 'Approve' : 'Reject'}
                </motion.button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default ProcedureRequests;
