import { NavLink, useParams } from 'react-router-dom';
import { useState, useEffect } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFileAlt, FaHistory, FaTooth, FaImages, FaCalendarAlt, FaFileSignature, FaClipboardCheck, FaFlask, FaUniversity, FaBuilding, FaTimes, FaCheck } from 'react-icons/fa';

const PatientNav = ({ selectedChart, setSelectedChart, charts }) => {
  const { nationalId } = useParams();
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showLabPopup, setShowLabPopup] = useState(false);
  const [selectedLabType, setSelectedLabType] = useState('');
  const [showSubmitForm, setShowSubmitForm] = useState(false);
  const [notes, setNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [labRequests, setLabRequests] = useState([]);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationMessage, setConfirmationMessage] = useState('');

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);
        setPatientData(response.data);
      } catch (err) {
        console.error('Error fetching patient data:', err.response?.status, err.response?.data);
        const message = err.response?.status === 404
          ? 'Patient not found'
          : err.response?.data?.message || 'Failed to load patient data';
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchPatientData();
    if (showLabPopup) {
      fetchLabRequests();
    }
  }, [nationalId, showLabPopup]);

  const fetchLabRequests = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/lab-requests/student', {
        headers: { Authorization: `Bearer ${token}` }
      });
      // Filter requests for current patient
      const patientRequests = response.data.filter(req => req.patientId === nationalId);
      setLabRequests(patientRequests);
    } catch (error) {
      console.error('Error fetching lab requests:', error);
    }
  };

  const handleLabTypeSelect = (labType) => {
    setSelectedLabType(labType);
    setShowSubmitForm(true);
  };

  const handleSubmitRequest = async () => {
    if (!selectedLabType || !patientData) return;

    setSubmitting(true);
    try {
      const token = localStorage.getItem('token');
      const requestData = {
        patientId: nationalId,
        patientName: patientData.fullName,
        labType: selectedLabType,
        notes: notes
      };

      await axios.post('http://localhost:5000/api/lab-requests', requestData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Reset form
      setSelectedLabType('');
      setShowSubmitForm(false);
      setNotes('');

      // Refresh lab requests
      fetchLabRequests();

      // Show confirmation popup
      setConfirmationMessage('Lab request submitted successfully!');
      setShowConfirmation(true);
    } catch (error) {
      console.error('Error submitting lab request:', error);
      setConfirmationMessage('Error submitting lab request. Please try again.');
      setShowConfirmation(true);
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'completed': return 'text-[#0077B6] bg-[rgba(0,119,182,0.1)]';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved': return <FaCheck className="h-4 w-4" />;
      case 'rejected': return <FaTimes className="h-4 w-4" />;
      default: return <FaFlask className="h-4 w-4" />;
    }
  };



  if (loading) {
    return (
      <div className="bg-white shadow-sm border-b border-gray-100 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="animate-pulse flex items-center space-x-4">
            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-32"></div>
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !patientData) {
    return (
      <div className="bg-white shadow-sm border-b border-gray-100 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="text-red-500">{error || 'Patient not found'}</div>
        </div>
      </div>
    );
  }

  const renderChartSelector = (isActive) => {
    if (!isActive || !charts || charts.length === 0) return null;
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="ml-2"
      >
        <select
          value={selectedChart?._id || ''}
          onChange={(e) => setSelectedChart(charts.find(chart => chart._id === e.target.value))}
          className="px-3 py-1.5 rounded-lg bg-white border border-gray-200 text-sm text-gray-600 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-200"
        >
          {charts.map(chart => (
            <option key={chart._id} value={chart._id}>
              {chart.title} - {new Date(chart.date).toLocaleDateString()} {chart.isLocked ? '(Locked)' : ''}
            </option>
          ))}
        </select>
      </motion.div>
    );
  };

  return (
    <>
      <motion.nav
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)] px-4 py-3 overflow-x-auto w-full"
      >
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <NavLink to={`/patientprofile/${nationalId}`} className="flex items-center w-full md:w-auto hover:opacity-80 transition-opacity">
          <div className="bg-[rgba(0,119,182,0.1)] p-2 rounded-lg mr-3 flex-shrink-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-[#0077B6]"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <div className="min-w-0">
            <h2 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Current Patient</h2>
            <h1 className="text-lg font-semibold text-gray-800 truncate">{patientData.fullName}</h1>
          </div>
        </NavLink>

        <div className="w-full md:w-auto mx-auto flex flex-col md:flex-row items-center gap-2">
          <ul className="flex flex-wrap justify-center gap-1 md:gap-2 w-full overflow-x-auto py-2">


            {/* Sheets */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/sheets`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaFileAlt className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Patient Examination Sheet</span>
              </NavLink>
            </li>

            {/* History */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/history`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaHistory className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">History</span>
              </NavLink>
            </li>

            {/* Tooth Chart */}
            <li className="flex items-center">
              <NavLink
                to={`/patientprofile/${nationalId}/toothchart`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaTooth className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Tooth Chart</span>
              </NavLink>
              {renderChartSelector(window.location.pathname.includes('/toothchart'))}
            </li>



            {/* Gallery */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/gallery`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaImages className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Gallery</span>
              </NavLink>
            </li>



            {/* Appointments */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/appointments`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaCalendarAlt className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Appointments</span>
              </NavLink>
            </li>

            {/* Consent */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/consent`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaFileSignature className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Consent</span>
              </NavLink>
            </li>

            {/* Review Steps */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/reviewsteps`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaClipboardCheck className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Review Steps</span>
              </NavLink>
            </li>

            {/* Lab */}
            <li>
              <button
                onClick={() => setShowLabPopup(true)}
                className="flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 text-gray-600 hover:bg-gray-50 hover:text-[#FF6B35]"
              >
                <FaFlask className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Lab</span>
              </button>
            </li>
          </ul>

        </div>

        <div className="hidden md:block w-48"></div>
      </div>
      </motion.nav>

      {/* Lab Popup */}
    {showLabPopup && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="bg-white rounded-xl shadow-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <FaFlask className="h-6 w-6 text-[#FF6B35] mr-3" />
              <h2 className="text-2xl font-bold text-gray-800">Lab Requests</h2>
            </div>
            <button
              onClick={() => {
                setShowLabPopup(false);
                setShowSubmitForm(false);
                setSelectedLabType('');
                setNotes('');
              }}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FaTimes className="h-6 w-6" />
            </button>
          </div>

          {patientData && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Patient Information</h3>
              <p className="text-gray-600">
                <span className="font-medium">Name:</span> {patientData.fullName}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">National ID:</span> {patientData.nationalId}
              </p>
            </div>
          )}

          {!showSubmitForm ? (
            <div className="grid md:grid-cols-2 gap-4 mb-6">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => handleLabTypeSelect('university')}
              >
                <div className="flex items-center mb-3">
                  <FaUniversity className="h-6 w-6 text-blue-600 mr-3" />
                  <h3 className="text-lg font-semibold text-blue-800">University Lab</h3>
                </div>
                <p className="text-blue-700 text-sm">
                  Submit request to the university laboratory for dental work and analysis.
                </p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200 cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => handleLabTypeSelect('outside')}
              >
                <div className="flex items-center mb-3">
                  <FaBuilding className="h-6 w-6 text-green-600 mr-3" />
                  <h3 className="text-lg font-semibold text-green-800">Outside Lab</h3>
                </div>
                <p className="text-green-700 text-sm">
                  Submit request to an external laboratory for specialized dental services.
                </p>
              </motion.div>
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-50 p-4 rounded-xl mb-6"
            >
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Submit Request - {selectedLabType === 'university' ? 'University Lab' : 'Outside Lab'}
              </h3>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes (Optional)
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35] focus:border-[#FF6B35]"
                  rows="3"
                  placeholder="Enter any additional notes or requirements..."
                />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={handleSubmitRequest}
                  disabled={submitting}
                  className="px-4 py-2 bg-[#FF6B35] text-white rounded-lg hover:bg-[#E55A2B] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Submitting...' : 'Submit Request'}
                </button>
                <button
                  onClick={() => {
                    setShowSubmitForm(false);
                    setSelectedLabType('');
                    setNotes('');
                  }}
                  className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          )}

          {/* Lab Requests History */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Request History</h3>
            {labRequests.length === 0 ? (
              <p className="text-gray-500 text-center py-6">No lab requests found for this patient.</p>
            ) : (
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {labRequests.map((request) => (
                  <motion.div
                    key={request._id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {getStatusIcon(request.status)}
                        <span className="ml-2 font-medium text-gray-800 text-sm">
                          {request.labType === 'university' ? 'University Lab' : 'Outside Lab'}
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mb-1">
                      <span className="font-medium">Submitted:</span> {new Date(request.submitDate).toLocaleDateString()}
                    </p>
                    {request.notes && (
                      <p className="text-xs text-gray-600 mb-1">
                        <span className="font-medium">Notes:</span> {request.notes}
                      </p>
                    )}
                    {request.responseNotes && (
                      <p className="text-xs text-gray-600">
                        <span className="font-medium">Response:</span> {request.responseNotes}
                      </p>
                    )}
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </div>
      )}

      {/* Confirmation Popup */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4"
          >
            <div className="text-center">
              <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${
                confirmationMessage.includes('Error') ? 'bg-red-100' : 'bg-green-100'
              }`}>
                {confirmationMessage.includes('Error') ? (
                  <FaTimes className="h-6 w-6 text-red-600" />
                ) : (
                  <FaCheck className="h-6 w-6 text-green-600" />
                )}
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {confirmationMessage.includes('Error') ? 'Error' : 'Success'}
              </h3>
              <p className="text-sm text-gray-500 mb-6">
                {confirmationMessage}
              </p>
              <button
                onClick={() => setShowConfirmation(false)}
                className="w-full px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005A8B] transition-colors"
              >
                OK
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </>
  );
};

export default PatientNav;