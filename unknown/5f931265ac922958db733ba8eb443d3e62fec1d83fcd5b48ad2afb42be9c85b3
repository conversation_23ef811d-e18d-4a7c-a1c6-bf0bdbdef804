import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AssistantSidebar from './AssistantSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaUsers, FaUserAlt, FaNotesMedical, FaCalendarAlt, FaPlus } from 'react-icons/fa';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const AssistantPatients = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [patients, setPatients] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [patientDetails, setPatientDetails] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [formData, setFormData] = useState({
    nationalId: '',
    fullName: '',
    phoneNumber: '',
    gender: '',
    age: '',
    address: '',
    occupation: '',
    drId: ''
  });
  const navigate = useNavigate();
  const { user, token } = useAuth();

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  // Function to fetch data
  const fetchData = async () => {
    if (!user || !token) {
      setError('Please log in to view data.');
      setLoading(false);
      return;
    }

    try {
      setRefreshing(true);
      const config = { headers: { Authorization: `Bearer ${token}` } };

      console.log('Fetching data for assistant:', user);

      // Fetch patients using the admin endpoint (now accessible to assistants)
      const patientsResponse = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/admin/patients`,
        config
      );

      // Fetch students for reference
      const studentsResponse = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/admin/students`,
        config
      );

      console.log('Patients response:', patientsResponse.data);
      console.log('Students response:', studentsResponse.data);

      setPatients(patientsResponse.data);
      setStudents(studentsResponse.data);
      setLoading(false);
      setError(''); // Clear any previous errors
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.message || 'Failed to fetch data');
      setLoading(false);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [user, token]);

  // Add focus event listener to refresh data when component is focused
  useEffect(() => {
    const handleFocus = () => {
      console.log('Patients component focused, refreshing data...');
      fetchData();
    };

    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [user, token]);

  // Listen for patient assignment notifications
  useEffect(() => {
    const checkPatientAssignment = () => {
      const assignedData = localStorage.getItem('patientAssigned');
      if (assignedData) {
        try {
          const data = JSON.parse(assignedData);
          const timeDiff = Date.now() - data.timestamp;
          
          // Only process if the assignment happened in the last 10 seconds
          if (timeDiff < 10000) {
            console.log('Patient assignment detected:', data);
            setSuccess(`Patient ${data.patientName} was successfully assigned to ${data.studentName}!`);
            
            // Refresh the data
            fetchData();
            
            // Clear the notification
            localStorage.removeItem('patientAssigned');
            
            // Clear success message after 5 seconds
            setTimeout(() => {
              setSuccess('');
            }, 5000);
          }
        } catch (error) {
          console.error('Error parsing patient assignment data:', error);
        }
      }
    };

    // Check immediately
    checkPatientAssignment();
    
    // Set up interval to check periodically
    const interval = setInterval(checkPatientAssignment, 2000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  // Fetch detailed patient info when a patient is selected
  useEffect(() => {
    const fetchPatientDetails = async () => {
      if (selectedPerson && selectedPerson.nationalId) {
        try {
          const config = { headers: { Authorization: `Bearer ${token}` } };
          // Use the public endpoint to get patient details
          const patientResponse = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/patients/public/${selectedPerson.nationalId}`,
            config
          );

          // Fetch appointments separately
          const appointmentsResponse = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/appointments/patient/${selectedPerson.nationalId}`,
            config
          );

          setPatientDetails({
            ...patientResponse.data,
            appointments: appointmentsResponse.data || []
          });
        } catch (err) {
          console.error('Error fetching patient details:', err);
          setPatientDetails(selectedPerson);
        }
      }
    };

    fetchPatientDetails();
  }, [selectedPerson, token]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddPatient = () => {
    setFormData({
      nationalId: '',
      fullName: '',
      phoneNumber: '',
      gender: '',
      age: '',
      address: '',
      occupation: '',
      drId: ''
    });
    setError(''); // Clear any previous errors
    setSuccess(''); // Clear any previous success messages
    setShowAddModal(true);
  };

  const handleSubmitPatient = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    const requiredFields = ['nationalId', 'fullName', 'phoneNumber', 'gender', 'age', 'address', 'occupation', 'drId'];
    const missingFields = requiredFields.filter(field => !formData[field] || formData[field].toString().trim() === '');
    
    if (missingFields.length > 0) {
      setError(`Please fill in all required fields: ${missingFields.join(', ')}`);
      return;
    }
    
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const patientData = {
        ...formData,
        age: parseInt(formData.age, 10), // Ensure age is a number
        medicalInfo: {
          chronicDiseases: [],
          recentSurgicalProcedures: '',
          currentMedications: '',
          chiefComplaint: ''
        }
      };

      console.log('Creating patient with data:', patientData);
      console.log('Form data validation:', {
        nationalId: formData.nationalId,
        fullName: formData.fullName,
        phoneNumber: formData.phoneNumber,
        gender: formData.gender,
        age: formData.age,
        address: formData.address,
        occupation: formData.occupation,
        drId: formData.drId
      });

      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/patients`,
        patientData,
        config
      );

      console.log('Patient creation response:', response.data);

      if (response.data && response.data.patient) {
        // Find the assigned student to add student information
        const assignedStudent = students.find(s => s.studentId === formData.drId);
        const newPatient = {
          ...response.data.patient,
          studentName: assignedStudent?.name || 'Unknown Student'
        };
        
        console.log('Adding new patient to state:', newPatient);
        setPatients(prevPatients => [...prevPatients, newPatient]);
        setShowAddModal(false);
        setFormData({
          nationalId: '',
          fullName: '',
          phoneNumber: '',
          gender: '',
          age: '',
          address: '',
          occupation: '',
          drId: ''
        });
        setError(''); // Clear any previous errors
        setSuccess('Patient added successfully!');
        
        // Refresh the data to ensure consistency
        setTimeout(() => {
          fetchData();
        }, 1000);
        
        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess('');
        }, 3000);
      }
    } catch (err) {
      console.error('Error creating patient:', err);
      console.error('Error response:', err.response?.data);
      const errorMessage = err.response?.data?.message || 'Failed to create patient';
      
      // Handle specific duplicate national ID error
      if (errorMessage.includes('already exists') || err.response?.status === 400) {
        setError('A patient with this National ID already exists. Please use a different National ID.');
      } else {
        setError(errorMessage);
      }
    }
  };

  const renderTableHeaders = () => (
    <>
      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">National ID</th>
      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Student</th>
    </>
  );

  const renderTableRow = (person) => (
    <>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{person.fullName}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{person.nationalId}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {person.studentName || students.find((s) => s.studentId === person.drId)?.name || 'None'}
      </td>
    </>
  );

  const renderModalContent = () => {
    if (!selectedPerson || !patientDetails) return null;
    
    return (
      <div className="space-y-6">
        <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
          <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
            <FaUserAlt className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
            Personal Information
          </h4>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Full Name</h4>
                <p className="text-sm text-gray-900 mt-1 font-medium">{patientDetails.fullName}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">National ID</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.nationalId}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Gender</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.gender}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Age</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.age}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Phone Number</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.phoneNumber}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Address</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.address}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Occupation</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.occupation}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Assigned Student</h4>
                <p className="text-sm text-gray-900 mt-1 font-medium">
                  {patientDetails.studentName || students.find((s) => s.studentId === patientDetails.drId)?.name || 'None'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Medical Information */}
        <div className={`bg-green-50 p-5 rounded-xl shadow-sm border border-green-100`}>
          <h4 className={`text-lg font-semibold text-[${websiteColorPalette.accent}] mb-4 flex items-center`}>
            <FaNotesMedical className={`h-5 w-5 mr-2 text-[${websiteColorPalette.accent}]`} />
            Medical Information
          </h4>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Medical History</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.medicalHistory || 'None'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Allergies</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.allergies || 'None'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Current Medications</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.currentMedications || 'None'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Chief Complaint</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.chiefComplaint || 'None'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Appointments */}
        {patientDetails.appointments && patientDetails.appointments.length > 0 && (
          <div className={`bg-purple-50 p-5 rounded-xl shadow-sm border border-purple-100`}>
            <h4 className={`text-lg font-semibold text-purple-600 mb-4 flex items-center`}>
              <FaCalendarAlt className="h-5 w-5 mr-2 text-purple-600" />
              Appointments
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="space-y-3">
                {patientDetails.appointments.map((appointment, index) => (
                  <div key={index} className="border-l-4 border-purple-400 pl-4 py-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {new Date(appointment.date).toLocaleDateString()} at {appointment.time}
                        </p>
                        <p className="text-sm text-gray-600">{appointment.reason || 'General consultation'}</p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {appointment.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-6" style={{ background: `linear-gradient(to bottom right, ${websiteColorPalette.primary}10, ${websiteColorPalette.background})` }}>
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-red-700">{error}</p>
                </div>
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-green-50 border-l-4 border-green-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-green-700">{success}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold mb-1" style={{ color: websiteColorPalette.primary }}>
                    Patients
                  </h1>
                  <p className="text-gray-600">View and manage all patients in your university</p>
                </div>
                <div className="flex mt-4 md:mt-0">
                  <button
                    onClick={handleAddPatient}
                    className="px-4 py-2 text-white rounded-lg transition-colors flex items-center hover:brightness-110 mr-3"
                    style={{
                      backgroundColor: websiteColorPalette.primary
                    }}
                  >
                    <FaPlus className="h-4 w-4 mr-2" />
                    <span>Add Patient</span>
                  </button>
                  <button
                    onClick={fetchData}
                    disabled={refreshing}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
                  </button>
                </div>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
              >
                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center">
                      <FaUsers className={`h-6 w-6 mr-3 text-[${websiteColorPalette.primary}]`} />
                      <h3 className="text-lg font-semibold text-gray-900">All Patients</h3>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>{renderTableHeaders()}</tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {patients.length === 0 ? (
                          <tr>
                            <td colSpan={3} className="px-6 py-8 text-center">
                              <div className="flex flex-col items-center justify-center">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-12 w-12 text-gray-400 mb-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                  />
                                </svg>
                                <h3 className="text-lg font-medium text-gray-900">No patients found</h3>
                                <p className="mt-1 text-gray-500">No patients registered in your university.</p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          patients.map((person) => (
                            <motion.tr
                              key={person._id}
                              variants={item}
                              className="hover:bg-gray-50 cursor-pointer"
                              onClick={() => setSelectedPerson(person)}
                            >
                              {renderTableRow(person)}
                            </motion.tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* Patient Details Modal */}
      {selectedPerson && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">Patient Details</h2>
              <button
                onClick={() => {
                  setSelectedPerson(null);
                  setPatientDetails(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6">
              {renderModalContent()}
            </div>
          </div>
        </div>
      )}

      {/* Add Patient Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">Add New Patient</h2>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitPatient} className="p-6 space-y-6">
              <h3 className="text-lg font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">
                Patient Information
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Full Name*</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">National ID*</label>
                  <input
                    type="text"
                    name="nationalId"
                    value={formData.nationalId}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Phone Number*</label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Gender*</label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  >
                    <option value="">Select</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Age*</label>
                  <input
                    type="number"
                    name="age"
                    value={formData.age}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Address*</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Occupation*</label>
                  <input
                    type="text"
                    name="occupation"
                    value={formData.occupation}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Assign to Student*</label>
                  <select
                    name="drId"
                    value={formData.drId}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  >
                    <option value="">Select Student</option>
                    {students.map((student) => (
                      <option key={student._id} value={student.studentId}>
                        {student.name} ({student.studentId})
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 text-white rounded-lg transition-colors hover:brightness-110"
                  style={{ backgroundColor: websiteColorPalette.primary }}
                >
                  Add Patient
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssistantPatients;
