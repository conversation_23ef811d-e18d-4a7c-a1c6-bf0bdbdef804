import { useState, useEffect, useRef } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import { FaImages, FaXRay, FaEdit, FaTrash, FaPlus, FaDownload } from 'react-icons/fa';
import Loader from '../components/Loader';

const Gallery = () => {
  const { token } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [images, setImages] = useState([]);
  const [xrays, setXrays] = useState([]);
  const [editingNote, setEditingNote] = useState(null);
  const [currentNote, setCurrentNote] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [activeTab, setActiveTab] = useState('gallery'); // 'gallery' or 'xray'
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });
  const fileInputRef = useRef(null);
  const { nationalId } = useParams();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (response.data) {
          setImages(response.data.galleryImages || []);
          setXrays(response.data.xrays || []);
        } else {
          setError('No data found for this patient');
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.response?.data?.message || 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    if (token && nationalId) {
      fetchData();
    }
  }, [nationalId, token]);

  const openImageModal = (image) => {
    setSelectedImage(image);
    setShowImageModal(true);
  };

  const handleImageUpload = async (e) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const formData = new FormData();
    Array.from(files).forEach(file => {
      if (activeTab === 'gallery') {
        formData.append('galleryImages', file);
      } else {
        formData.append('xrays', file);
      }
    });

    try {
      setLoading(true);
      const endpoint = activeTab === 'gallery'
        ? `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/gallery`
        : `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/xrays`;

      const response = await axios.post(
        endpoint,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          },
        }
      );

      if (response.data) {
        if (activeTab === 'gallery') {
          setImages(response.data);
        } else {
          setXrays(response.data);
        }

        // Show success notification
        setNotification({
          show: true,
          message: `${activeTab === 'gallery' ? 'Images' : 'X-rays'} uploaded successfully!`,
          type: 'success'
        });

        // Hide notification after 3 seconds
        setTimeout(() => {
          setNotification({ show: false, message: '', type: 'success' });
        }, 3000);
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError(err.response?.data?.message || `Failed to upload ${activeTab === 'gallery' ? 'images' : 'X-rays'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleAddNote = async (id) => {
    if (!currentNote.trim()) {
      setEditingNote(null);
      return;
    }

    try {
      const endpoint = activeTab === 'gallery'
        ? `http://localhost:5000/api/patients/${nationalId}/gallery/note`
        : `http://localhost:5000/api/patients/${nationalId}/xrays/note`;

      const payload = activeTab === 'gallery'
        ? { imageId: id, note: currentNote }
        : { xrayId: id, note: currentNote };

      const response = await axios.put(
        endpoint,
        payload,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data) {
        if (activeTab === 'gallery') {
          setImages(images.map(img => img._id === id ? { ...img, note: currentNote } : img));
        } else {
          setXrays(xrays.map(xray => xray._id === id ? { ...xray, note: currentNote } : xray));
        }
      }

      setEditingNote(null);
      setCurrentNote('');
    } catch (err) {
      console.error('Note update error:', err);
      setError(err.response?.data?.message || 'Failed to update note');
    }
  };

  const confirmDeleteItem = (item) => {
    setItemToDelete(item);
    setShowDeleteModal(true);
  };

  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    try {
      const endpoint = activeTab === 'gallery'
        ? `http://localhost:5000/api/patients/${nationalId}/gallery/${itemToDelete._id}`
        : `http://localhost:5000/api/patients/${nationalId}/xrays/${itemToDelete._id}`;

      await axios.delete(endpoint, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (activeTab === 'gallery') {
        setImages(images.filter(img => img._id !== itemToDelete._id));
      } else {
        setXrays(xrays.filter(xray => xray._id !== itemToDelete._id));
      }

      setShowDeleteModal(false);
      setItemToDelete(null);

      // Show success notification
      setNotification({
        show: true,
        message: `${activeTab === 'gallery' ? 'Image' : 'X-ray'} deleted successfully!`,
        type: 'success'
      });

      // Hide notification after 3 seconds
      setTimeout(() => {
        setNotification({ show: false, message: '', type: 'success' });
      }, 3000);
    } catch (err) {
      console.error('Delete error:', err);
      setError(err.response?.data?.message || `Failed to delete ${activeTab === 'gallery' ? 'image' : 'X-ray'}`);
      setShowDeleteModal(false);
    }
  };

  const triggerFileInput = () => fileInputRef.current.click();

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  const currentItems = activeTab === 'gallery' ? images : xrays;

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-7xl mx-auto">
            {notification.show && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`mb-4 p-4 rounded-lg ${
                  notification.type === 'success' ? 'bg-[#28A745]/20 text-[#28A745]' : 'bg-red-100 text-red-800'
                }`}
              >
                {notification.message}
              </motion.div>
            )}

            {error && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-6 p-4 bg-red-100 text-[#333333] rounded-lg"
              >
                {error}
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Patient Gallery
                  </h1>
                  <p className="text-gray-600">Manage patient images and X-rays</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={triggerFileInput}
                  className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <FaPlus className="h-5 w-5 mr-2" />
                  Add {activeTab === 'gallery' ? 'Images' : 'X-rays'}
                </motion.button>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                  className="hidden"
                  accept="image/*"
                  multiple
                />
              </div>

              {/* Tab Switcher */}
              <div className="flex justify-center mb-8">
                <div className="bg-white rounded-full p-1 shadow-md flex">
                  <button
                    onClick={() => setActiveTab('gallery')}
                    className={`px-6 py-2 rounded-full flex items-center transition-all duration-300 ${
                      activeTab === 'gallery'
                        ? 'bg-[#0077B6] text-white shadow-md'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <FaImages className="mr-2" />
                    Gallery
                  </button>
                  <button
                    onClick={() => setActiveTab('xray')}
                    className={`px-6 py-2 rounded-full flex items-center transition-all duration-300 ${
                      activeTab === 'xray'
                        ? 'bg-[#20B2AA] text-white shadow-md'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <FaXRay className="mr-2" />
                    X-Rays
                  </button>
                </div>
              </div>

              {currentItems.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-8 text-center bg-white rounded-xl shadow-sm"
                >
                  <div className="mx-auto h-16 w-16 text-gray-400 mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">No {activeTab === 'gallery' ? 'images' : 'X-rays'} found</h3>
                  <p className="text-gray-500">Get started by adding new {activeTab === 'gallery' ? 'gallery images' : 'X-ray images'}.</p>
                </motion.div>
              ) : (
                <motion.div
                  variants={container}
                  initial="hidden"
                  animate="show"
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  {currentItems.map((item) => (
                    <motion.div
                      key={item._id}
                      variants={item}
                      className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
                    >
                      <div className="relative group">
                        <img
                          src={item.url.startsWith('http') ? item.url : `http://localhost:5000/${item.url}`}
                          alt={activeTab === 'gallery' ? 'Gallery' : 'X-Ray'}
                          className={`w-full h-48 ${activeTab === 'gallery' ? 'object-cover' : 'object-contain bg-black'} cursor-pointer`}
                          onClick={() => openImageModal(item)}
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                          <button
                            onClick={() => openImageModal(item)}
                            className="bg-white text-[#0077B6] p-2 rounded-full mx-1 hover:bg-[#0077B6]/10"
                          >
                            <FaPlus className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => confirmDeleteItem(item)}
                            className="bg-white text-red-600 p-2 rounded-full mx-1 hover:bg-red-50"
                          >
                            <FaTrash className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="flex justify-between items-start">
                          <span className="text-sm text-gray-500">
                            {new Date(item.date).toLocaleDateString()}
                          </span>
                          <button
                            onClick={() => {
                              setEditingNote(item._id);
                              setCurrentNote(item.note || '');
                            }}
                            className="text-[#0077B6] hover:text-[#20B2AA] text-sm font-medium flex items-center"
                          >
                            <FaEdit className="h-3 w-3 mr-1" />
                            {item.note ? 'Edit Note' : 'Add Note'}
                          </button>
                        </div>
                        {editingNote === item._id ? (
                          <div className="mt-2">
                            <textarea
                              value={currentNote}
                              onChange={(e) => setCurrentNote(e.target.value)}
                              className="w-full border border-gray-300 rounded-lg p-2 text-sm focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                              rows="2"
                              placeholder={`Add a note about this ${activeTab === 'gallery' ? 'image' : 'X-ray'}...`}
                            />
                            <div className="flex justify-end gap-2 mt-2">
                              <button
                                onClick={() => setEditingNote(null)}
                                className="text-sm text-gray-600 hover:text-gray-800 px-2 py-1"
                              >
                                Cancel
                              </button>
                              <button
                                onClick={() => handleAddNote(item._id)}
                                className="bg-[#0077B6] text-white px-3 py-1 rounded-md text-sm hover:bg-[#0077B6]/80"
                              >
                                Save
                              </button>
                            </div>
                          </div>
                        ) : (
                          item.note && <p className="mt-2 text-sm text-gray-700">{item.note}</p>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </motion.div>
          </div>
        </main>
      </div>



      {/* Full-size image modal */}
      {showImageModal && selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl w-full">
            <button
              onClick={() => setShowImageModal(false)}
              className="absolute top-4 right-4 bg-white rounded-full p-2 text-gray-800 hover:bg-gray-200"
            >
              ✕
            </button>
            <img
              src={selectedImage.url.startsWith('http') ? selectedImage.url : `http://localhost:5000/${selectedImage.url}`}
              alt={activeTab === 'gallery' ? 'Gallery Full View' : 'X-Ray Full View'}
              className={`w-full max-h-[80vh] ${activeTab === 'gallery' ? 'object-contain' : 'object-contain bg-black'} rounded-lg`}
            />
            <div className="bg-white p-4 rounded-b-lg">
              <p className="text-sm text-gray-500">
                {new Date(selectedImage.date).toLocaleDateString()}
              </p>
              {selectedImage.note && (
                <p className="mt-2 text-gray-700">{selectedImage.note}</p>
              )}
              <div className="mt-4 flex justify-end">
                <a
                  href={selectedImage.url.startsWith('http') ? selectedImage.url : `http://localhost:5000/${selectedImage.url}`}
                  download
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-[#0077B6] hover:text-[#20B2AA]"
                >
                  <FaDownload className="mr-2" />
                  Download
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl w-full max-w-md p-6 shadow-xl"
          >
            <h3 className="text-xl font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this {activeTab === 'gallery' ? 'image' : 'X-ray'}? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setItemToDelete(null);
                }}
                className="px-4 py-2 text-gray-700 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteItem}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default Gallery;