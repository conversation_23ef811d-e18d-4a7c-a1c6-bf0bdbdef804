{"name": "intraoral-patient-dashboard", "version": "1.0.0", "description": "Patient dashboard for intraoral device with live video calling and YOLOv8 integration", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "cors": "^2.8.5", "express": "^4.18.2", "framer-motion": "^10.16.4", "multer": "^1.4.5-lts.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-icons": "^5.5.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "react-webcam": "^7.1.1", "simple-peer": "^9.11.1", "socket.io-client": "^4.7.2", "styled-components": "^6.0.7", "ultralytics": "^1.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server.js", "dev": "concurrently \"npm run server\" \"npm run start\"", "dev:server": "npm run server", "dev:frontend": "npm run start", "install:all": "npm install && npm install ultralytics"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "devDependencies": {"concurrently": "^8.2.0", "tailwindcss": "^3.4.17"}}