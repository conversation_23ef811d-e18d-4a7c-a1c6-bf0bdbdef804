import React, { useRef, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { QRCodeSVG } from 'qrcode.react';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';
import { FaTooth } from 'react-icons/fa';


const Confirmation = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const { appointmentDetails } = location.state || {};
  const printRef = useRef();
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [universityInfo, setUniversityInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Static Dentlyzer information
  const dentlyzerInfo = {
    name: 'Dently<PERSON>',
    logo: '/imgs/dentlyzer-logo.png',
    email: '<EMAIL>',
    phone: '+20 1276902211',
  };

  useEffect(() => {
    setIsClient(true);
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch university information from DB
  // Fetch university information from DB
useEffect(() => {
  const fetchUniversityInfo = async () => {
    if (appointmentDetails?.universityClinic) {
      setIsLoading(true);
      try {
        const response = await fetch(
          `${process.env.REACT_APP_API_URL}/api/universities?universityId=${encodeURIComponent(
            appointmentDetails.universityClinic
          )}`
        );
        if (!response.ok) {
          throw new Error(`Failed to fetch university: ${response.statusText}`);
        }
        const universities = await response.json();
        if (universities.length === 0) {
          throw new Error('University not found');
        }
        const universityData = universities[0];
        console.log('University data:', universityData); // Debug log
        if (!universityData.contactInfo) {
          console.warn('contactInfo missing in university data:', universityData);
        }
        setUniversityInfo({
          name: universityData.name?.[i18n.language] || universityData.universityId || 'Unknown University',
          email: universityData.contactInfo?.email,
          phone: universityData.contactInfo?.phone,
          logo: universityData.logo || '/imgs/default-university-logo.png',
        });
      } catch (error) {
        console.error('Error fetching university info:', error.message, error.stack);
        setError(t('appointment.FailedLoadUniversityInfo'));
      } finally {
        setIsLoading(false);
      }
    }
  };
  fetchUniversityInfo();
}, [appointmentDetails?.universityClinic, t, i18n.language]);

  const downloadPDF = async () => {
    const input = printRef.current;

    // Ensure the element is fully rendered
    input.style.display = 'block';

    try {
      const canvas = await html2canvas(input, {
        scale: 2,
        logging: false,
        useCORS: true,
        backgroundColor: '#ffffff',
      });

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      const imgWidth = 190;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(canvas, 'PNG', 10, 10, imgWidth, imgHeight);

      const filename =
        i18n.language === 'ar'
          ? `${t('appointment.AppointmentConfirmation')}-${appointmentDetails?.universityClinic || ''}-${appointmentDetails?.appointmentId || ''}.pdf`
          : `${appointmentDetails?.universityClinic || ''}-${t('appointment.AppointmentConfirmation')}-${appointmentDetails?.appointmentId || ''}.pdf`;

      pdf.save(filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
      setError(t('appointment.FailedGeneratePDF'));
    } finally {
      input.style.display = '';
    }
  };

  return (
    <div
      className={`min-h-screen flex flex-col bg-[#0077B6] bg-opacity-5 ${i18n.language === 'ar' ? 'text-right' : 'text-left'}`}
      dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}
      style={{ fontFamily: i18n.language === 'ar' ? 'sans-serif' : 'inherit' }}
    >
      <Navbar />
      <main className="flex-grow pt-16 md:pt-24 pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white shadow-lg rounded-xl overflow-hidden border border-[#20B2AA] border-opacity-30">
            <div className="p-4 md:p-8 text-center">
              <div className="mx-auto flex items-center justify-center h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#28A745] bg-opacity-20 mb-3 md:mb-4">
                <svg className="h-5 w-5 md:h-6 md:w-6 text-[#28A745]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-xl md:text-2xl font-bold text-[#0077B6] mb-2">
                {t('appointment.AppointmentConfirmed')}
              </h1>
              <p className="text-sm md:text-base text-[#333333] mb-4 md:mb-6">
                {t('appointment.SuccessfullyBookedUniversity')}
              </p>

              {error && (
                <div className="mb-6 p-4 bg-red-100 rounded-xl text-red-800">
                  {error}
                </div>
              )}

              <div ref={printRef} className="bg-white p-4 md:p-6 mx-auto text-left">
                <div className={`flex ${i18n.language === 'ar' ? 'flex-row-reverse' : 'flex-row'} justify-between items-center mb-4 md:mb-6 border-b pb-4 md:pb-6`}>
                  <div className="flex items-center">
                    {isLoading ? (
                      <Loader />
                    ) : universityInfo ? (
                      <div>
                        <h2 className="text-lg md:text-xl font-bold text-[#0077B6]">{universityInfo.name}</h2>
                        <p className="text-xs md:text-sm text-[#333333]">{t('appointment.UniversityDentalCare')}</p>
                      </div>
                    ) : (
                      <div>
                        <h2 className="text-lg md:text-xl font-bold text-[#0077B6]">
                          {appointmentDetails?.universityClinic || t('appointment.UnknownUniversity')}
                        </h2>
                        <p className="text-xs md:text-sm text-[#333333]">{t('appointment.UniversityDentalCare')}</p>
                      </div>
                    )}
                    {universityInfo && (
                      <img
                        src={universityInfo.logo}
                        alt={`${universityInfo.name} Logo`}
                        className={`h-10 md:h-14 object-contain ${i18n.language === 'ar' ? 'mr-2 md:mr-4' : 'ml-2 md:ml-4'}`}
                      />
                    )}
                  </div>
                  <div className={`flex items-center ${i18n.language === 'ar' ? 'text-left' : 'text-right'}`}>
                    <div>
                      <h2 className="text-lg md:text-xl font-bold text-[#0077B6]">DENT<span className="text-lg md:text-xl font-bold text-[#20B2AA]">LYZER</span></h2>
                      <p className="text-xs md:text-sm text-[#333333]">{t('appointment.DentalBookingPlatform')}</p>
                    </div>
                     <FaTooth className="w-10 h-10 text-[#0077B6]" />

                  </div>
                </div>

                <div className="border-b border-[#20B2AA] border-opacity-30 pb-3 md:pb-4 mb-4 md:mb-6">
                  <h3 className="text-base md:text-lg font-semibold text-center text-[#0077B6]">
                    {t('appointment.AppointmentConfirmation')}
                  </h3>
                </div>

                {appointmentDetails && (
                  <div className="space-y-3 md:space-y-4 mb-6 md:mb-8">
                    {[
                      { label: t('appointment.AppointmentID'), value: appointmentDetails.appointmentId, highlight: true },
                      { label: t('appointment.UniversityClinic'), value: appointmentDetails.universityClinic },
                      { label: t('appointment.Date'), value: appointmentDetails.date },
                      { label: t('appointment.Time'), value: appointmentDetails.timeSlot },
                      { label: t('appointment.PatientName'), value: appointmentDetails.fullName },
                      { label: t('appointment.PhoneNumber'), value: appointmentDetails.phoneNumber },
                      { label: t('appointment.NationalID'), value: appointmentDetails.nationalId },
                      { label: t('appointment.Age'), value: appointmentDetails.age },
                      { label: t('appointment.Gender'), value: appointmentDetails.gender },
                      { label: t('appointment.ChiefComplaint'), value: appointmentDetails.chiefComplaint },
                      { label: t('appointment.Occupation'), value: appointmentDetails.occupation || 'N/A' },
                      { label: t('appointment.Address'), value: appointmentDetails.address || 'N/A' },
                    ].map((item, index) => (
                      <div
                        key={index}
                        className={`flex ${i18n.language === 'ar' ? 'flex-row-reverse' : 'flex-row'} ${
                          isMobile && i18n.language === 'ar' ? 'flex-col' : 'justify-between'
                        } text-sm md:text-base mb-2`}
                      >
                        <span className={`font-medium ${item.highlight ? 'text-[#0077B6]' : 'text-[#333333]'}`}>
                          {item.label}:
                        </span>
                        <span
                          className={`${item.highlight ? 'font-bold text-[#0077B6]' : ''} ${
                            isMobile && i18n.language === 'ar' ? 'mt-1' : ''
                          } break-all ${isMobile && i18n.language === 'ar' ? 'w-full' : 'max-w-[50%]'} ${
                            i18n.language === 'ar' ? 'text-right' : 'text-left'
                          }`}
                        >
                          {item.value}
                        </span>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex flex-col items-center mt-6 md:mt-8 mb-4 md:mb-6">
                  <div className="p-3 md:p-4 bg-[#0077B6] bg-opacity-10 rounded-lg mb-3 md:mb-4">
                    {isClient && (
                      <QRCodeSVG
                        value={
                          appointmentDetails
                            ? JSON.stringify({
                                id: appointmentDetails.appointmentId,
                                name: appointmentDetails.fullName,
                                universityClinic: appointmentDetails.universityClinic,
                                date: appointmentDetails.date,
                                time: appointmentDetails.timeSlot,
                              })
                            : ''
                        }
                        size={isMobile ? 100 : 140}
                        level="H"
                        includeMargin={true}
                      />
                    )}
                  </div>
                  <p className="text-xs md:text-sm text-[#333333] text-opacity-70">{t('appointment.ScanQRCode')}</p>
                </div>

                <div
                  className={`mt-6 md:mt-8 pt-4 md:pt-6 border-t border-[#20B2AA] border-opacity-30 text-xs md:text-sm ${
                    i18n.language === 'ar' ? 'text-right' : 'text-left'
                  }`}
                >
                  <div className="mb-4 md:mb-6">
                    <h4 className="font-medium mb-1 md:mb-2 text-[#0077B6]">{t('appointment.UniversityClinicContact')}</h4>
                    {isLoading ? (
                      <Loader />
                    ) : universityInfo ? (
                      <>
                        <p className="mb-1 text-[#333333]">
                          {t('appointment.Email')}: {universityInfo.email}
                        </p>
                        <p className="text-[#333333]">
                          {t('appointment.Phone')}: {universityInfo.phone}
                        </p>
                      </>
                    ) : (
                      <p className="text-[#333333] text-opacity-70">{t('appointment.NoContactInfoAvailable')}</p>
                    )}
                  </div>
                  <div className="mb-4 md:mb-6">
                    <h4 className="font-medium mb-1 md:mb-2 text-[#0077B6]">{t('appointment.DentlyzerContact')}</h4>
                    <p className="mb-1 text-[#333333]">
                      {t('appointment.Email')}: {dentlyzerInfo.email}
                    </p>
                    <p className="text-[#333333]">
                      {t('appointment.Phone')}: {dentlyzerInfo.phone}
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 md:mt-8 flex flex-col sm:flex-row justify-center gap-3 md:gap-4">
                <button
                  onClick={downloadPDF}
                  className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 border border-transparent text-xs md:text-sm font-medium rounded-md shadow-sm text-white bg-[#0077B6] hover:bg-[#0066A0] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0077B6]"
                >
                  <svg
                    className="h-4 w-4 md:h-5 md:w-5 mr-1 md:mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                    />
                  </svg>
                  {t('appointment.DownloadConfirmation')}
                </button>
                <a
                  href="/universities"
                  className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 border border-[#20B2AA] border-opacity-50 text-xs md:text-sm font-medium rounded-md shadow-sm text-[#333333] bg-white hover:bg-[#0077B6] hover:bg-opacity-5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0077B6]"
                >
                  {t('appointment.BackToUniversities')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Confirmation;