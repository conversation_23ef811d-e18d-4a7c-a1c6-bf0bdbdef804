{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\YOLODetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaCalendarAlt, FaTooth, FaEye, FaServer } from 'react-icons/fa';\nimport './YOLODetection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst YOLODetection = ({\n  onResults,\n  isAnalyzing,\n  currentImage\n}) => {\n  _s();\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const [serverStatus, setServerStatus] = useState('checking');\n  const classColors = {\n    'decaycavity': '#ff6b6b',\n    'early-decay': '#ffd43b',\n    'healthy tooth': '#51cf66'\n  };\n  const classIcons = {\n    'decaycavity': /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n      className: \"h-6 w-6 text-red-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 20\n    }, this),\n    'early-decay': /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n      className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 20\n    }, this),\n    'healthy tooth': /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n      className: \"h-6 w-6 text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 22\n    }, this)\n  };\n\n  // Check server status\n  const checkServerStatus = useCallback(async () => {\n    try {\n      const response = await fetch('/api/health');\n      if (response.ok) {\n        const data = await response.json();\n        setServerStatus('connected');\n        console.log('✅ Server connected:', data);\n      } else {\n        setServerStatus('error');\n        console.log('❌ Server health check failed');\n      }\n    } catch (error) {\n      setServerStatus('disconnected');\n      console.log('❌ Server not reachable:', error.message);\n    }\n  }, []);\n  useEffect(() => {\n    checkServerStatus();\n    // Check server status every 30 seconds\n    const interval = setInterval(checkServerStatus, 30000);\n    return () => clearInterval(interval);\n  }, [checkServerStatus]);\n  useEffect(() => {\n    // Update current image when prop changes\n    if (currentImage) {\n      console.log('📸 Received new captured image for analysis');\n    }\n  }, [currentImage]);\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\n    results.forEach((result, index) => {\n      // Use actual bbox coordinates if available, otherwise generate random ones\n      let x, y, width, height;\n      if (result.bbox) {\n        x = result.bbox.x * canvasWidth;\n        y = result.bbox.y * canvasHeight;\n        width = result.bbox.width * canvasWidth;\n        height = result.bbox.height * canvasHeight;\n      } else {\n        // Fallback to random coordinates for demo\n        x = Math.random() * (canvasWidth - 100);\n        y = Math.random() * (canvasHeight - 100);\n        width = 80 + Math.random() * 40;\n        height = 80 + Math.random() * 40;\n      }\n\n      // Set box color based on class\n      const color = classColors[result.class] || '#0077B6';\n\n      // Draw bounding box\n      ctx.strokeStyle = color;\n      ctx.lineWidth = 3;\n      ctx.strokeRect(x, y, width, height);\n\n      // Draw label background\n      const label = `${result.class} (${(result.confidence * 100).toFixed(1)}%)`;\n      ctx.font = '14px Arial';\n      const textWidth = ctx.measureText(label).width;\n      ctx.fillStyle = color;\n      ctx.fillRect(x, y - 25, textWidth + 10, 20);\n\n      // Draw label text\n      ctx.fillStyle = 'white';\n      ctx.fillText(label, x + 5, y - 10);\n    });\n  }, [classColors]);\n  const generateAnnotatedImage = useCallback(results => {\n    // Create a canvas to draw the annotated image\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    canvas.width = 640;\n    canvas.height = 480;\n\n    // If we have a current image, use it as background\n    if (currentImage) {\n      const img = new Image();\n      img.onload = () => {\n        // Draw the original image\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n        // Draw detection boxes on top\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n\n        // Set the annotated image\n        setAnnotatedImage(canvas.toDataURL());\n      };\n      img.src = currentImage;\n    } else {\n      // Fallback to placeholder if no image\n      ctx.fillStyle = '#f5f5f5';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Draw detection boxes\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n      setAnnotatedImage(canvas.toDataURL());\n    }\n  }, [currentImage, drawDetectionBoxes]);\n  const handleDetectionResults = useCallback(results => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\n    };\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = {\n      ...detectionStats\n    };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Generate annotated image\n    generateAnnotatedImage(results);\n\n    // Notify parent component\n    onResults(results);\n  }, [currentImage, detectionStats, onResults, generateAnnotatedImage]);\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#51cf66';\n    if (confidence >= 0.6) return '#ffd43b';\n    return '#ff6b6b';\n  };\n  const getServerStatusColor = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'text-green-600';\n      case 'disconnected':\n        return 'text-red-600';\n      case 'error':\n        return 'text-yellow-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  const getServerStatusText = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'Connected';\n      case 'disconnected':\n        return 'Disconnected';\n      case 'error':\n        return 'Error';\n      default:\n        return 'Checking...';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaBrain, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"AI Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), \"Model: best.pt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\",\n          children: \"Classes: 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-3 py-1 bg-[rgba(0,119,182,0.1)] rounded-full text-xs font-medium flex items-center ${getServerStatusColor()}`,\n          children: [/*#__PURE__*/_jsxDEV(FaServer, {\n            className: \"inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), getServerStatusText()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), serverStatus === 'disconnected' && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-red-50 border border-red-200 p-4 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"h-5 w-5 text-red-500 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-red-800\",\n            children: \"YOLOv8 Server Not Connected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-700 mt-1\",\n            children: [\"Please start the backend server to enable AI analysis. Run: \", /*#__PURE__*/_jsxDEV(\"code\", {\n              className: \"bg-red-100 px-2 py-1 rounded\",\n              children: \"npm run server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 77\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this), isAnalyzing && serverStatus === 'connected' && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#0077B6] font-medium\",\n          children: \"Analyzing image with YOLOv8...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 9\n    }, this), !isAnalyzing && serverStatus === 'disconnected' && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gray-50 p-6 rounded-lg border border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaServer, {\n          className: \"h-8 w-8 text-gray-400 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"AI analysis unavailable - server not connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }, this), annotatedImage && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.95\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Annotated Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Annotated detection\",\n          className: \"w-full h-auto max-h-64 object-contain\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Detection Statistics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n        children: Object.entries(detectionStats).map(([className, count]) => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mr-3\",\n              children: classIcons[className]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700 capitalize\",\n                children: className.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-[#0077B6]\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)\n        }, className, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaClock, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Recent Detections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 max-h-64 overflow-y-auto\",\n        children: detectionHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(FaEye, {\n            className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 font-medium mb-2\",\n            children: \"No detections yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-sm\",\n            children: \"Start video stream to begin analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this) : detectionHistory.map(detection => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                children: /*#__PURE__*/_jsxDEV(FaTooth, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: [detection.results.length, \" detection(s)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: new Date(detection.timestamp).toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: detection.results.slice(0, 3).map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [classIcons[result.class], /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 text-xs font-medium text-gray-600\",\n                  children: [(result.confidence * 100).toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 17\n          }, this)\n        }, detection.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n_s(YOLODetection, \"XBPb2iLihM1swQWokoBknzz44yw=\");\n_c = YOLODetection;\nexport default YOLODetection;\nvar _c;\n$RefreshReg$(_c, \"YOLODetection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "FaBrain", "FaChartBar", "FaClock", "FaImage", "FaRobot", "FaExclamationTriangle", "FaCheckCircle", "FaTimesCircle", "FaCalendarAlt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaEye", "FaServer", "jsxDEV", "_jsxDEV", "YOLODetection", "onResults", "isAnalyzing", "currentImage", "_s", "detectionHistory", "setDetectionHistory", "annotatedImage", "setAnnotatedImage", "detectionStats", "setDetectionStats", "decaycavity", "serverStatus", "setServerStatus", "classColors", "classIcons", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "checkServerStatus", "response", "fetch", "ok", "data", "json", "console", "log", "error", "message", "interval", "setInterval", "clearInterval", "drawDetectionBoxes", "ctx", "results", "canvasWidth", "canvasHeight", "for<PERSON>ach", "result", "index", "x", "y", "width", "height", "bbox", "Math", "random", "color", "class", "strokeStyle", "lineWidth", "strokeRect", "label", "confidence", "toFixed", "font", "textWidth", "measureText", "fillStyle", "fillRect", "fillText", "generateAnnotatedImage", "canvas", "document", "createElement", "getContext", "img", "Image", "onload", "drawImage", "toDataURL", "src", "handleDetectionResults", "newDetection", "id", "Date", "now", "timestamp", "toISOString", "image", "prev", "slice", "newStats", "getConfidenceColor", "getServerStatusColor", "getServerStatusText", "children", "div", "initial", "opacity", "animate", "scale", "alt", "Object", "entries", "map", "count", "whileHover", "replace", "length", "detection", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/YOLODetection.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaCalendarAlt, Fa<PERSON>ooth, FaEye, FaServer } from 'react-icons/fa';\r\nimport './YOLODetection.css';\r\n\r\nconst YOLODetection = ({ onResults, isAnalyzing, currentImage }) => {\r\n  const [detectionHistory, setDetectionHistory] = useState([]);\r\n  const [annotatedImage, setAnnotatedImage] = useState(null);\r\n  const [detectionStats, setDetectionStats] = useState({\r\n    decaycavity: 0,\r\n    'early-decay': 0,\r\n    'healthy tooth': 0\r\n  });\r\n  const [serverStatus, setServerStatus] = useState('checking');\r\n\r\n  const classColors = {\r\n    'decaycavity': '#ff6b6b',\r\n    'early-decay': '#ffd43b',\r\n    'healthy tooth': '#51cf66'\r\n  };\r\n\r\n  const classIcons = {\r\n    'decaycavity': <FaTimesCircle className=\"h-6 w-6 text-red-500\" />,\r\n    'early-decay': <FaExclamationTriangle className=\"h-6 w-6 text-yellow-500\" />,\r\n    'healthy tooth': <FaCheckCircle className=\"h-6 w-6 text-green-500\" />\r\n  };\r\n\r\n  // Check server status\r\n  const checkServerStatus = useCallback(async () => {\r\n    try {\r\n      const response = await fetch('/api/health');\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setServerStatus('connected');\r\n        console.log('✅ Server connected:', data);\r\n      } else {\r\n        setServerStatus('error');\r\n        console.log('❌ Server health check failed');\r\n      }\r\n    } catch (error) {\r\n      setServerStatus('disconnected');\r\n      console.log('❌ Server not reachable:', error.message);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    checkServerStatus();\r\n    // Check server status every 30 seconds\r\n    const interval = setInterval(checkServerStatus, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [checkServerStatus]);\r\n\r\n  useEffect(() => {\r\n    // Update current image when prop changes\r\n    if (currentImage) {\r\n      console.log('📸 Received new captured image for analysis');\r\n    }\r\n  }, [currentImage]);\r\n\r\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\r\n    results.forEach((result, index) => {\r\n      // Use actual bbox coordinates if available, otherwise generate random ones\r\n      let x, y, width, height;\r\n      \r\n      if (result.bbox) {\r\n        x = result.bbox.x * canvasWidth;\r\n        y = result.bbox.y * canvasHeight;\r\n        width = result.bbox.width * canvasWidth;\r\n        height = result.bbox.height * canvasHeight;\r\n      } else {\r\n        // Fallback to random coordinates for demo\r\n        x = Math.random() * (canvasWidth - 100);\r\n        y = Math.random() * (canvasHeight - 100);\r\n        width = 80 + Math.random() * 40;\r\n        height = 80 + Math.random() * 40;\r\n      }\r\n\r\n      // Set box color based on class\r\n      const color = classColors[result.class] || '#0077B6';\r\n\r\n      // Draw bounding box\r\n      ctx.strokeStyle = color;\r\n      ctx.lineWidth = 3;\r\n      ctx.strokeRect(x, y, width, height);\r\n\r\n      // Draw label background\r\n      const label = `${result.class} (${(result.confidence * 100).toFixed(1)}%)`;\r\n      ctx.font = '14px Arial';\r\n      const textWidth = ctx.measureText(label).width;\r\n\r\n      ctx.fillStyle = color;\r\n      ctx.fillRect(x, y - 25, textWidth + 10, 20);\r\n\r\n      // Draw label text\r\n      ctx.fillStyle = 'white';\r\n      ctx.fillText(label, x + 5, y - 10);\r\n    });\r\n  }, [classColors]);\r\n\r\n  const generateAnnotatedImage = useCallback((results) => {\r\n    // Create a canvas to draw the annotated image\r\n    const canvas = document.createElement('canvas');\r\n    const ctx = canvas.getContext('2d');\r\n\r\n    // Set canvas size\r\n    canvas.width = 640;\r\n    canvas.height = 480;\r\n\r\n    // If we have a current image, use it as background\r\n    if (currentImage) {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        // Draw the original image\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n\r\n        // Draw detection boxes on top\r\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n\r\n        // Set the annotated image\r\n        setAnnotatedImage(canvas.toDataURL());\r\n      };\r\n      img.src = currentImage;\r\n    } else {\r\n      // Fallback to placeholder if no image\r\n      ctx.fillStyle = '#f5f5f5';\r\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n\r\n      // Draw detection boxes\r\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n\r\n      setAnnotatedImage(canvas.toDataURL());\r\n    }\r\n  }, [currentImage, drawDetectionBoxes]);\r\n\r\n  const handleDetectionResults = useCallback((results) => {\r\n    const newDetection = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\r\n    };\r\n\r\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\r\n\r\n    // Update stats\r\n    const newStats = { ...detectionStats };\r\n    results.forEach(result => {\r\n      newStats[result.class]++;\r\n    });\r\n    setDetectionStats(newStats);\r\n\r\n    // Generate annotated image\r\n    generateAnnotatedImage(results);\r\n\r\n    // Notify parent component\r\n    onResults(results);\r\n  }, [currentImage, detectionStats, onResults, generateAnnotatedImage]);\r\n\r\n  const getConfidenceColor = (confidence) => {\r\n    if (confidence >= 0.8) return '#51cf66';\r\n    if (confidence >= 0.6) return '#ffd43b';\r\n    return '#ff6b6b';\r\n  };\r\n\r\n  const getServerStatusColor = () => {\r\n    switch (serverStatus) {\r\n      case 'connected': return 'text-green-600';\r\n      case 'disconnected': return 'text-red-600';\r\n      case 'error': return 'text-yellow-600';\r\n      default: return 'text-gray-600';\r\n    }\r\n  };\r\n\r\n  const getServerStatusText = () => {\r\n    switch (serverStatus) {\r\n      case 'connected': return 'Connected';\r\n      case 'disconnected': return 'Disconnected';\r\n      case 'error': return 'Error';\r\n      default: return 'Checking...';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Model Info */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaBrain className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">AI Analysis</h3>\r\n        </div>\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\r\n            <FaRobot className=\"inline mr-1\" />\r\n            Model: best.pt\r\n          </span>\r\n          <span className=\"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\">\r\n            Classes: 3\r\n          </span>\r\n          <span className={`px-3 py-1 bg-[rgba(0,119,182,0.1)] rounded-full text-xs font-medium flex items-center ${getServerStatusColor()}`}>\r\n            <FaServer className=\"inline mr-1\" />\r\n            {getServerStatusText()}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Server Status Warning */}\r\n      {serverStatus === 'disconnected' && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-red-50 border border-red-200 p-4 rounded-lg\"\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <FaExclamationTriangle className=\"h-5 w-5 text-red-500 mr-3\" />\r\n            <div>\r\n              <h4 className=\"text-sm font-medium text-red-800\">YOLOv8 Server Not Connected</h4>\r\n              <p className=\"text-sm text-red-700 mt-1\">\r\n                Please start the backend server to enable AI analysis. Run: <code className=\"bg-red-100 px-2 py-1 rounded\">npm run server</code>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Analysis Status */}\r\n      {isAnalyzing && serverStatus === 'connected' && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\"\r\n        >\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"loading-spinner mr-3\"></div>\r\n            <p className=\"text-[#0077B6] font-medium\">Analyzing image with YOLOv8...</p>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* No Analysis Available */}\r\n      {!isAnalyzing && serverStatus === 'disconnected' && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-gray-50 p-6 rounded-lg border border-gray-200\"\r\n        >\r\n          <div className=\"flex items-center justify-center\">\r\n            <FaServer className=\"h-8 w-8 text-gray-400 mr-3\" />\r\n            <p className=\"text-gray-600 font-medium\">AI analysis unavailable - server not connected</p>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Annotated Image */}\r\n      {annotatedImage && (\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\"\r\n        >\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n              <FaImage className=\"h-4 w-4\" />\r\n            </div>\r\n            <h4 className=\"text-lg font-semibold text-[#0077B6]\">Annotated Results</h4>\r\n          </div>\r\n          <div className=\"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\">\r\n            <img\r\n              src={annotatedImage}\r\n              alt=\"Annotated detection\"\r\n              className=\"w-full h-auto max-h-64 object-contain\"\r\n            />\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Detection Statistics */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaChartBar className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Detection Statistics</h4>\r\n        </div>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n          {Object.entries(detectionStats).map(([className, count]) => (\r\n            <motion.div\r\n              key={className}\r\n              whileHover={{ scale: 1.05 }}\r\n              className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n            >\r\n              <div className=\"flex items-center\">\r\n                <div className=\"mr-3\">{classIcons[className]}</div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-700 capitalize\">{className.replace('-', ' ')}</p>\r\n                  <p className=\"text-2xl font-bold text-[#0077B6]\">{count}</p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detection History */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaClock className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Recent Detections</h4>\r\n        </div>\r\n\r\n        <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n          {detectionHistory.length === 0 ? (\r\n            <div className=\"text-center py-8\">\r\n              <FaEye className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n              <p className=\"text-gray-600 font-medium mb-2\">No detections yet</p>\r\n              <p className=\"text-gray-500 text-sm\">Start video stream to begin analysis</p>\r\n            </div>\r\n          ) : (\r\n            detectionHistory.map((detection) => (\r\n              <motion.div\r\n                key={detection.id}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n              >\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaTooth className=\"h-4 w-4\" />\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-900\">\r\n                        {detection.results.length} detection(s)\r\n                      </p>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        {new Date(detection.timestamp).toLocaleTimeString()}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    {detection.results.slice(0, 3).map((result, index) => (\r\n                      <div key={index} className=\"flex items-center\">\r\n                        {classIcons[result.class]}\r\n                        <span className=\"ml-1 text-xs font-medium text-gray-600\">\r\n                          {(result.confidence * 100).toFixed(0)}%\r\n                        </span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default YOLODetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,gBAAgB;AAC7K,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC;IACnD6B,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,UAAU,CAAC;EAE5D,MAAMgC,WAAW,GAAG;IAClB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,aAAa,eAAEhB,OAAA,CAACN,aAAa;MAACuB,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjE,aAAa,eAAErB,OAAA,CAACR,qBAAqB;MAACyB,SAAS,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5E,eAAe,eAAErB,OAAA,CAACP,aAAa;MAACwB,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtE,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGrC,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAa,CAAC;MAC3C,IAAID,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCb,eAAe,CAAC,WAAW,CAAC;QAC5Bc,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,CAAC;MAC1C,CAAC,MAAM;QACLZ,eAAe,CAAC,OAAO,CAAC;QACxBc,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhB,eAAe,CAAC,cAAc,CAAC;MAC/Bc,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,KAAK,CAACC,OAAO,CAAC;IACvD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN/C,SAAS,CAAC,MAAM;IACdsC,iBAAiB,CAAC,CAAC;IACnB;IACA,MAAMU,QAAQ,GAAGC,WAAW,CAACX,iBAAiB,EAAE,KAAK,CAAC;IACtD,OAAO,MAAMY,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACV,iBAAiB,CAAC,CAAC;EAEvBtC,SAAS,CAAC,MAAM;IACd;IACA,IAAIoB,YAAY,EAAE;MAChBwB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC,EAAE,CAACzB,YAAY,CAAC,CAAC;EAElB,MAAM+B,kBAAkB,GAAGlD,WAAW,CAAC,CAACmD,GAAG,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,KAAK;IAClFF,OAAO,CAACG,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACjC;MACA,IAAIC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM;MAEvB,IAAIL,MAAM,CAACM,IAAI,EAAE;QACfJ,CAAC,GAAGF,MAAM,CAACM,IAAI,CAACJ,CAAC,GAAGL,WAAW;QAC/BM,CAAC,GAAGH,MAAM,CAACM,IAAI,CAACH,CAAC,GAAGL,YAAY;QAChCM,KAAK,GAAGJ,MAAM,CAACM,IAAI,CAACF,KAAK,GAAGP,WAAW;QACvCQ,MAAM,GAAGL,MAAM,CAACM,IAAI,CAACD,MAAM,GAAGP,YAAY;MAC5C,CAAC,MAAM;QACL;QACAI,CAAC,GAAGK,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIX,WAAW,GAAG,GAAG,CAAC;QACvCM,CAAC,GAAGI,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIV,YAAY,GAAG,GAAG,CAAC;QACxCM,KAAK,GAAG,EAAE,GAAGG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QAC/BH,MAAM,GAAG,EAAE,GAAGE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;MAClC;;MAEA;MACA,MAAMC,KAAK,GAAGnC,WAAW,CAAC0B,MAAM,CAACU,KAAK,CAAC,IAAI,SAAS;;MAEpD;MACAf,GAAG,CAACgB,WAAW,GAAGF,KAAK;MACvBd,GAAG,CAACiB,SAAS,GAAG,CAAC;MACjBjB,GAAG,CAACkB,UAAU,CAACX,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC;;MAEnC;MACA,MAAMS,KAAK,GAAG,GAAGd,MAAM,CAACU,KAAK,KAAK,CAACV,MAAM,CAACe,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;MAC1ErB,GAAG,CAACsB,IAAI,GAAG,YAAY;MACvB,MAAMC,SAAS,GAAGvB,GAAG,CAACwB,WAAW,CAACL,KAAK,CAAC,CAACV,KAAK;MAE9CT,GAAG,CAACyB,SAAS,GAAGX,KAAK;MACrBd,GAAG,CAAC0B,QAAQ,CAACnB,CAAC,EAAEC,CAAC,GAAG,EAAE,EAAEe,SAAS,GAAG,EAAE,EAAE,EAAE,CAAC;;MAE3C;MACAvB,GAAG,CAACyB,SAAS,GAAG,OAAO;MACvBzB,GAAG,CAAC2B,QAAQ,CAACR,KAAK,EAAEZ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,EAAE,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,WAAW,CAAC,CAAC;EAEjB,MAAMiD,sBAAsB,GAAG/E,WAAW,CAAEoD,OAAO,IAAK;IACtD;IACA,MAAM4B,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAM/B,GAAG,GAAG6B,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAH,MAAM,CAACpB,KAAK,GAAG,GAAG;IAClBoB,MAAM,CAACnB,MAAM,GAAG,GAAG;;IAEnB;IACA,IAAI1C,YAAY,EAAE;MAChB,MAAMiE,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjB;QACAnC,GAAG,CAACoC,SAAS,CAACH,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAACpB,KAAK,EAAEoB,MAAM,CAACnB,MAAM,CAAC;;QAErD;QACAX,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAE4B,MAAM,CAACpB,KAAK,EAAEoB,MAAM,CAACnB,MAAM,CAAC;;QAE7D;QACArC,iBAAiB,CAACwD,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;MACvC,CAAC;MACDJ,GAAG,CAACK,GAAG,GAAGtE,YAAY;IACxB,CAAC,MAAM;MACL;MACAgC,GAAG,CAACyB,SAAS,GAAG,SAAS;MACzBzB,GAAG,CAAC0B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEG,MAAM,CAACpB,KAAK,EAAEoB,MAAM,CAACnB,MAAM,CAAC;;MAE/C;MACAX,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAE4B,MAAM,CAACpB,KAAK,EAAEoB,MAAM,CAACnB,MAAM,CAAC;MAE7DrC,iBAAiB,CAACwD,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACrE,YAAY,EAAE+B,kBAAkB,CAAC,CAAC;EAEtC,MAAMwC,sBAAsB,GAAG1F,WAAW,CAAEoD,OAAO,IAAK;IACtD,MAAMuC,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnC5C,OAAO,EAAEA,OAAO;MAChB6C,KAAK,EAAE9E,YAAY,IAAI;IACzB,CAAC;IAEDG,mBAAmB,CAAC4E,IAAI,IAAI,CAACP,YAAY,EAAE,GAAGO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhE;IACA,MAAMC,QAAQ,GAAG;MAAE,GAAG3E;IAAe,CAAC;IACtC2B,OAAO,CAACG,OAAO,CAACC,MAAM,IAAI;MACxB4C,QAAQ,CAAC5C,MAAM,CAACU,KAAK,CAAC,EAAE;IAC1B,CAAC,CAAC;IACFxC,iBAAiB,CAAC0E,QAAQ,CAAC;;IAE3B;IACArB,sBAAsB,CAAC3B,OAAO,CAAC;;IAE/B;IACAnC,SAAS,CAACmC,OAAO,CAAC;EACpB,CAAC,EAAE,CAACjC,YAAY,EAAEM,cAAc,EAAER,SAAS,EAAE8D,sBAAsB,CAAC,CAAC;EAErE,MAAMsB,kBAAkB,GAAI9B,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC;EAED,MAAM+B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQ1E,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,gBAAgB;MACzC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,OAAO;QAAE,OAAO,iBAAiB;MACtC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAM2E,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQ3E,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,WAAW;MACpC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B;QAAS,OAAO,aAAa;IAC/B;EACF,CAAC;EAED,oBACEb,OAAA;IAAKiB,SAAS,EAAC,WAAW;IAAAwE,QAAA,gBAExBzF,OAAA;MAAKiB,SAAS,EAAC,6EAA6E;MAAAwE,QAAA,gBAC1FzF,OAAA;QAAKiB,SAAS,EAAC,mBAAmB;QAAAwE,QAAA,gBAChCzF,OAAA;UAAKiB,SAAS,EAAC,6DAA6D;UAAAwE,QAAA,eAC1EzF,OAAA,CAACb,OAAO;YAAC8B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNrB,OAAA;UAAIiB,SAAS,EAAC,sCAAsC;UAAAwE,QAAA,EAAC;QAAW;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNrB,OAAA;QAAKiB,SAAS,EAAC,YAAY;QAAAwE,QAAA,gBACzBzF,OAAA;UAAMiB,SAAS,EAAC,oFAAoF;UAAAwE,QAAA,gBAClGzF,OAAA,CAACT,OAAO;YAAC0B,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPrB,OAAA;UAAMiB,SAAS,EAAC,qFAAqF;UAAAwE,QAAA,EAAC;QAEtG;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPrB,OAAA;UAAMiB,SAAS,EAAE,yFAAyFsE,oBAAoB,CAAC,CAAC,EAAG;UAAAE,QAAA,gBACjIzF,OAAA,CAACF,QAAQ;YAACmB,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnCmE,mBAAmB,CAAC,CAAC;QAAA;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLR,YAAY,KAAK,cAAc,iBAC9Bb,OAAA,CAACd,MAAM,CAACwG,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEhD,CAAC,EAAE;MAAG,CAAE;MAC/BiD,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEhD,CAAC,EAAE;MAAE,CAAE;MAC9B3B,SAAS,EAAC,gDAAgD;MAAAwE,QAAA,eAE1DzF,OAAA;QAAKiB,SAAS,EAAC,mBAAmB;QAAAwE,QAAA,gBAChCzF,OAAA,CAACR,qBAAqB;UAACyB,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DrB,OAAA;UAAAyF,QAAA,gBACEzF,OAAA;YAAIiB,SAAS,EAAC,kCAAkC;YAAAwE,QAAA,EAAC;UAA2B;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFrB,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAwE,QAAA,GAAC,8DACqB,eAAAzF,OAAA;cAAMiB,SAAS,EAAC,8BAA8B;cAAAwE,QAAA,EAAC;YAAc;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGAlB,WAAW,IAAIU,YAAY,KAAK,WAAW,iBAC1Cb,OAAA,CAACd,MAAM,CAACwG,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEhD,CAAC,EAAE;MAAG,CAAE;MAC/BiD,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEhD,CAAC,EAAE;MAAE,CAAE;MAC9B3B,SAAS,EAAC,oFAAoF;MAAAwE,QAAA,eAE9FzF,OAAA;QAAKiB,SAAS,EAAC,kCAAkC;QAAAwE,QAAA,gBAC/CzF,OAAA;UAAKiB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5CrB,OAAA;UAAGiB,SAAS,EAAC,4BAA4B;UAAAwE,QAAA,EAAC;QAA8B;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGA,CAAClB,WAAW,IAAIU,YAAY,KAAK,cAAc,iBAC9Cb,OAAA,CAACd,MAAM,CAACwG,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEhD,CAAC,EAAE;MAAG,CAAE;MAC/BiD,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEhD,CAAC,EAAE;MAAE,CAAE;MAC9B3B,SAAS,EAAC,kDAAkD;MAAAwE,QAAA,eAE5DzF,OAAA;QAAKiB,SAAS,EAAC,kCAAkC;QAAAwE,QAAA,gBAC/CzF,OAAA,CAACF,QAAQ;UAACmB,SAAS,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDrB,OAAA;UAAGiB,SAAS,EAAC,2BAA2B;UAAAwE,QAAA,EAAC;QAA8C;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGAb,cAAc,iBACbR,OAAA,CAACd,MAAM,CAACwG,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAK,CAAE;MACrCD,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAE,CAAE;MAClC7E,SAAS,EAAC,0CAA0C;MAAAwE,QAAA,gBAEpDzF,OAAA;QAAKiB,SAAS,EAAC,wBAAwB;QAAAwE,QAAA,gBACrCzF,OAAA;UAAKiB,SAAS,EAAC,6DAA6D;UAAAwE,QAAA,eAC1EzF,OAAA,CAACV,OAAO;YAAC2B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNrB,OAAA;UAAIiB,SAAS,EAAC,sCAAsC;UAAAwE,QAAA,EAAC;QAAiB;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNrB,OAAA;QAAKiB,SAAS,EAAC,sEAAsE;QAAAwE,QAAA,eACnFzF,OAAA;UACE0E,GAAG,EAAElE,cAAe;UACpBuF,GAAG,EAAC,qBAAqB;UACzB9E,SAAS,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAGDrB,OAAA;MAAKiB,SAAS,EAAC,0CAA0C;MAAAwE,QAAA,gBACvDzF,OAAA;QAAKiB,SAAS,EAAC,wBAAwB;QAAAwE,QAAA,gBACrCzF,OAAA;UAAKiB,SAAS,EAAC,6DAA6D;UAAAwE,QAAA,eAC1EzF,OAAA,CAACZ,UAAU;YAAC6B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNrB,OAAA;UAAIiB,SAAS,EAAC,sCAAsC;UAAAwE,QAAA,EAAC;QAAoB;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACNrB,OAAA;QAAKiB,SAAS,EAAC,uCAAuC;QAAAwE,QAAA,EACnDO,MAAM,CAACC,OAAO,CAACvF,cAAc,CAAC,CAACwF,GAAG,CAAC,CAAC,CAACjF,SAAS,EAAEkF,KAAK,CAAC,kBACrDnG,OAAA,CAACd,MAAM,CAACwG,GAAG;UAETU,UAAU,EAAE;YAAEN,KAAK,EAAE;UAAK,CAAE;UAC5B7E,SAAS,EAAC,6GAA6G;UAAAwE,QAAA,eAEvHzF,OAAA;YAAKiB,SAAS,EAAC,mBAAmB;YAAAwE,QAAA,gBAChCzF,OAAA;cAAKiB,SAAS,EAAC,MAAM;cAAAwE,QAAA,EAAEzE,UAAU,CAACC,SAAS;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDrB,OAAA;cAAAyF,QAAA,gBACEzF,OAAA;gBAAGiB,SAAS,EAAC,8CAA8C;gBAAAwE,QAAA,EAAExE,SAAS,CAACoF,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAnF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FrB,OAAA;gBAAGiB,SAAS,EAAC,mCAAmC;gBAAAwE,QAAA,EAAEU;cAAK;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAVDJ,SAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrB,OAAA;MAAKiB,SAAS,EAAC,0CAA0C;MAAAwE,QAAA,gBACvDzF,OAAA;QAAKiB,SAAS,EAAC,wBAAwB;QAAAwE,QAAA,gBACrCzF,OAAA;UAAKiB,SAAS,EAAC,6DAA6D;UAAAwE,QAAA,eAC1EzF,OAAA,CAACX,OAAO;YAAC4B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNrB,OAAA;UAAIiB,SAAS,EAAC,sCAAsC;UAAAwE,QAAA,EAAC;QAAiB;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAENrB,OAAA;QAAKiB,SAAS,EAAC,oCAAoC;QAAAwE,QAAA,EAChDnF,gBAAgB,CAACgG,MAAM,KAAK,CAAC,gBAC5BtG,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAwE,QAAA,gBAC/BzF,OAAA,CAACH,KAAK;YAACoB,SAAS,EAAC;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DrB,OAAA;YAAGiB,SAAS,EAAC,gCAAgC;YAAAwE,QAAA,EAAC;UAAiB;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnErB,OAAA;YAAGiB,SAAS,EAAC,uBAAuB;YAAAwE,QAAA,EAAC;UAAoC;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,GAENf,gBAAgB,CAAC4F,GAAG,CAAEK,SAAS,iBAC7BvG,OAAA,CAACd,MAAM,CAACwG,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEjD,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCkD,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEjD,CAAC,EAAE;UAAE,CAAE;UAC9B1B,SAAS,EAAC,6GAA6G;UAAAwE,QAAA,eAEvHzF,OAAA;YAAKiB,SAAS,EAAC,mCAAmC;YAAAwE,QAAA,gBAChDzF,OAAA;cAAKiB,SAAS,EAAC,mBAAmB;cAAAwE,QAAA,gBAChCzF,OAAA;gBAAKiB,SAAS,EAAC,6DAA6D;gBAAAwE,QAAA,eAC1EzF,OAAA,CAACJ,OAAO;kBAACqB,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACNrB,OAAA;gBAAAyF,QAAA,gBACEzF,OAAA;kBAAGiB,SAAS,EAAC,mCAAmC;kBAAAwE,QAAA,GAC7Cc,SAAS,CAAClE,OAAO,CAACiE,MAAM,EAAC,eAC5B;gBAAA;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJrB,OAAA;kBAAGiB,SAAS,EAAC,uBAAuB;kBAAAwE,QAAA,EACjC,IAAIX,IAAI,CAACyB,SAAS,CAACvB,SAAS,CAAC,CAACwB,kBAAkB,CAAC;gBAAC;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrB,OAAA;cAAKiB,SAAS,EAAC,6BAA6B;cAAAwE,QAAA,EACzCc,SAAS,CAAClE,OAAO,CAAC+C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACc,GAAG,CAAC,CAACzD,MAAM,EAAEC,KAAK,kBAC/C1C,OAAA;gBAAiBiB,SAAS,EAAC,mBAAmB;gBAAAwE,QAAA,GAC3CzE,UAAU,CAACyB,MAAM,CAACU,KAAK,CAAC,eACzBnD,OAAA;kBAAMiB,SAAS,EAAC,wCAAwC;kBAAAwE,QAAA,GACrD,CAAChD,MAAM,CAACe,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAJCqB,KAAK;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA7BDkF,SAAS,CAAC1B,EAAE;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BP,CACb;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAnWIJ,aAAa;AAAAwG,EAAA,GAAbxG,aAAa;AAqWnB,eAAeA,aAAa;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}