{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ToastContainer, toast } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { motion } from 'framer-motion';\nimport { FaTooth, FaVideo, FaUser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt, FaServer } from 'react-icons/fa';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport Sidebar from './components/Sidebar';\nimport Navbar from './components/Navbar';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  var _detectionResults$;\n  const [isConnected, setIsConnected] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [serverStatus, setServerStatus] = useState('checking');\n  const [patientInfo] = useState({\n    name: 'John Doe',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\n  const [analysisHistory, setAnalysisHistory] = useState([]);\n\n  // Check server status\n  const checkServerStatus = async () => {\n    try {\n      const response = await fetch('/api/health');\n      if (response.ok) {\n        const data = await response.json();\n        setServerStatus('connected');\n        console.log('✅ YOLOv8 server connected:', data);\n      } else {\n        setServerStatus('error');\n        console.log('❌ YOLOv8 server health check failed');\n      }\n    } catch (error) {\n      setServerStatus('disconnected');\n      console.log('❌ YOLOv8 server not reachable:', error.message);\n    }\n  };\n  useEffect(() => {\n    checkServerStatus();\n    // Check server status every 30 seconds\n    const interval = setInterval(checkServerStatus, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const handleConnectionStatus = status => {\n    setIsConnected(status);\n    if (status) {\n      toast.success('Connected to patient successfully!');\n    } else {\n      toast.error('Connection lost. Trying to reconnect...');\n    }\n  };\n  const handleDetectionResults = (results, annotatedImagePath) => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n\n    // Add to history\n    const newAnalysis = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentCapturedImage,\n      annotatedImage: annotatedImagePath,\n      patientId: patientInfo.id,\n      patientName: patientInfo.name\n    };\n    setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\n\n    if (results.length > 0) {\n      const classNames = results.map(result => result.class);\n      toast.info(`Detected: ${classNames.join(', ')}`);\n    }\n  };\n  const startAnalysis = () => {\n    if (serverStatus !== 'connected') {\n      toast.error('YOLOv8 server not connected. Please start the backend server first.');\n      return;\n    }\n    setIsAnalyzing(true);\n    toast.info('Starting dental analysis...');\n  };\n  const handleImageCaptured = imageSrc => {\n    setCurrentCapturedImage(imageSrc);\n  };\n  const getServerStatusColor = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'text-green-600';\n      case 'disconnected':\n        return 'text-red-600';\n      case 'error':\n        return 'text-yellow-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  const getServerStatusText = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'YOLOv8 Connected';\n      case 'disconnected':\n        return 'YOLOv8 Disconnected';\n      case 'error':\n        return 'YOLOv8 Error';\n      default:\n        return 'Checking...';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen),\n        isConnected: isConnected\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Intraoral Patient Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Real-time dental analysis and consultation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getServerStatusColor()}`,\n                    children: [/*#__PURE__*/_jsxDEV(FaServer, {\n                      className: \"mr-1 h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 23\n                    }, this), getServerStatusText()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: startAnalysis,\n                  disabled: isAnalyzing || serverStatus !== 'connected',\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: [/*#__PURE__*/_jsxDEV(FaBrain, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this), isAnalyzing ? 'Analyzing...' : 'Start Analysis']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), serverStatus !== 'connected' && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-red-600 text-center\",\n                  children: \"YOLOv8 server required for analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Patient\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: patientInfo.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Video Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: isConnected ? 'Live' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Detections\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: detectionResults.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Analyses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: analysisHistory.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-full ${serverStatus === 'connected' ? 'bg-green-100 text-green-600' : serverStatus === 'error' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(FaServer, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"YOLOv8 Server\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-2xl font-bold ${getServerStatusColor()}`,\n                      children: serverStatus === 'connected' ? 'Online' : serverStatus === 'error' ? 'Error' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Live Video Consultation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(VideoCall, {\n                  onConnectionStatus: handleConnectionStatus,\n                  onStartAnalysis: startAnalysis,\n                  onImageCaptured: handleImageCaptured,\n                  onDetectionResults: handleDetectionResults\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Patient Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(PatientInfo, {\n                  patient: patientInfo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaBrain, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"AI Dental Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YOLODetection, {\n                  onResults: handleDetectionResults,\n                  isAnalyzing: isAnalyzing,\n                  currentImage: currentCapturedImage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Analysis Results\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnalysisResults, {\n                  results: detectionResults,\n                  isAnalyzing: isAnalyzing,\n                  history: analysisHistory\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.4\n              },\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-[#0077B6]\",\n                  children: \"Patient Summary & History\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Current Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Total Detections\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 346,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-lg font-bold text-[#0077B6]\",\n                          children: detectionResults.length\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Primary Issue\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 350,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-semibold text-gray-900 capitalize\",\n                          children: ((_detectionResults$ = detectionResults[0]) === null || _detectionResults$ === void 0 ? void 0 : _detectionResults$.class.replace('-', ' ')) || 'None'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 351,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2\",\n                      children: detectionResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700 capitalize\",\n                          children: result.class.replace('-', ' ')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 360,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-bold text-[#0077B6]\",\n                          children: [(result.confidence * 100).toFixed(1), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 359,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No current analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Start analysis to see summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: (() => {\n                      const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n                      const recommendations = [];\n                      if (decayCount > 0) {\n                        recommendations.push({\n                          type: 'urgent',\n                          title: 'Immediate Treatment',\n                          description: 'Cavities require immediate dental treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                            className: \"h-4 w-4 text-red-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 401,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (earlyDecayCount > 0) {\n                        recommendations.push({\n                          type: 'warning',\n                          title: 'Preventive Care',\n                          description: 'Schedule follow-up for preventive treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                            className: \"h-4 w-4 text-yellow-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 410,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (healthyCount > 0) {\n                        recommendations.push({\n                          type: 'positive',\n                          title: 'Good Oral Health',\n                          description: 'Continue regular oral hygiene routine.',\n                          icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                            className: \"h-4 w-4 text-green-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 419,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      recommendations.push({\n                        type: 'info',\n                        title: 'Regular Checkup',\n                        description: 'Schedule next checkup within 6 months.',\n                        icon: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                          className: \"h-4 w-4 text-blue-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 427,\n                          columnNumber: 35\n                        }, this)\n                      });\n                      return recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mr-3 mt-1\",\n                          children: rec.icon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 432,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: rec.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 436,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-gray-600\",\n                            children: rec.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 437,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 435,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 29\n                      }, this));\n                    })()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Complete analysis for recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 456,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Analysis History\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this), analysisHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                    children: [analysisHistory.slice(0, 5).map((historyItem, index) => {\n                      const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\n                      let severity = 'low';\n                      let icon = /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                        className: \"h-4 w-4 text-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 469,\n                        columnNumber: 38\n                      }, this);\n                      if (decayCount > 0) {\n                        severity = 'high';\n                        icon = /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                          className: \"h-4 w-4 text-red-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 473,\n                          columnNumber: 36\n                        }, this);\n                      } else if (earlyDecayCount > 0) {\n                        severity = 'medium';\n                        icon = /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                          className: \"h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 476,\n                          columnNumber: 36\n                        }, this);\n                      }\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mr-3\",\n                            children: icon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 482,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm font-medium text-gray-900\",\n                              children: [historyItem.results.length, \" detection(s)\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 486,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-xs text-gray-500\",\n                              children: new Date(historyItem.timestamp).toLocaleDateString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 489,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 485,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 481,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\",\n                          children: severity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 494,\n                          columnNumber: 31\n                        }, this)]\n                      }, historyItem.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 480,\n                        columnNumber: 29\n                      }, this);\n                    }), analysisHistory.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center pt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\"+\", analysisHistory.length - 5, \" more analyses\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No analysis history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Perform analyses to build history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"TRXlZPZ5ZLLb4P/JXy7DLB02iIo=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ToastContainer", "toast", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaVideo", "FaUser", "FaChartBar", "FaBrain", "FaClock", "FaEye", "FaSearch", "FaHistory", "FaInfoCircle", "FaTimesCircle", "FaExclamationTriangle", "FaCheckCircle", "FaCalendarAlt", "FaServer", "VideoCall", "YOLODetection", "PatientInfo", "AnalysisResults", "Sidebar", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "_detectionResults$", "isConnected", "setIsConnected", "sidebarOpen", "setSidebarOpen", "serverStatus", "setServerStatus", "patientInfo", "name", "id", "age", "lastVisit", "detectionResults", "setDetectionResults", "isAnalyzing", "setIsAnalyzing", "currentCapturedImage", "setCurrentCapturedImage", "analysisHistory", "setAnalysisHistory", "checkServerStatus", "response", "fetch", "ok", "data", "json", "console", "log", "error", "message", "interval", "setInterval", "clearInterval", "handleConnectionStatus", "status", "success", "handleDetectionResults", "results", "annotatedImagePath", "newAnalysis", "Date", "now", "timestamp", "toISOString", "image", "annotatedImage", "patientId", "patientName", "prev", "slice", "length", "classNames", "map", "result", "class", "info", "join", "startAnalysis", "handleImageCaptured", "imageSrc", "getServerStatusColor", "getServerStatusText", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "opacity", "animate", "transition", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "disabled", "y", "delay", "x", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "onDetectionResults", "patient", "onResults", "currentImage", "history", "replace", "index", "confidence", "toFixed", "decayCount", "filter", "r", "earlyDecayCount", "healthyCount", "recommendations", "push", "type", "title", "description", "icon", "rec", "historyItem", "severity", "toLocaleDateString", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ToastContainer, toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport { motion } from 'framer-motion';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaVideo, FaUser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt, FaServer } from 'react-icons/fa';\r\nimport VideoCall from './components/VideoCall';\r\nimport YOLODetection from './components/YOLODetection';\r\nimport PatientInfo from './components/PatientInfo';\r\nimport AnalysisResults from './components/AnalysisResults';\r\nimport Sidebar from './components/Sidebar';\r\nimport Navbar from './components/Navbar';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [serverStatus, setServerStatus] = useState('checking');\r\n  const [patientInfo] = useState({\r\n    name: 'John Doe',\r\n    id: 'P001',\r\n    age: 35,\r\n    lastVisit: '2024-01-15'\r\n  });\r\n  const [detectionResults, setDetectionResults] = useState([]);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\r\n  const [analysisHistory, setAnalysisHistory] = useState([]);\r\n\r\n  // Check server status\r\n  const checkServerStatus = async () => {\r\n    try {\r\n      const response = await fetch('/api/health');\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setServerStatus('connected');\r\n        console.log('✅ YOLOv8 server connected:', data);\r\n      } else {\r\n        setServerStatus('error');\r\n        console.log('❌ YOLOv8 server health check failed');\r\n      }\r\n    } catch (error) {\r\n      setServerStatus('disconnected');\r\n      console.log('❌ YOLOv8 server not reachable:', error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    checkServerStatus();\r\n    // Check server status every 30 seconds\r\n    const interval = setInterval(checkServerStatus, 30000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const handleConnectionStatus = (status) => {\r\n    setIsConnected(status);\r\n    if (status) {\r\n      toast.success('Connected to patient successfully!');\r\n    } else {\r\n      toast.error('Connection lost. Trying to reconnect...');\r\n    }\r\n  };\r\n\r\n  const handleDetectionResults = (results, annotatedImagePath) => {\r\n    setDetectionResults(results);\r\n    setIsAnalyzing(false);\r\n    \r\n    // Add to history\r\n    const newAnalysis = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentCapturedImage,\r\n      annotatedImage: annotatedImagePath,\r\n      patientId: patientInfo.id,\r\n      patientName: patientInfo.name\r\n    };\r\n    \r\n    setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\r\n    \r\n    if (results.length > 0) {\r\n      const classNames = results.map(result => result.class);\r\n      toast.info(`Detected: ${classNames.join(', ')}`);\r\n    }\r\n  };\r\n\r\n  const startAnalysis = () => {\r\n    if (serverStatus !== 'connected') {\r\n      toast.error('YOLOv8 server not connected. Please start the backend server first.');\r\n      return;\r\n    }\r\n    \r\n    setIsAnalyzing(true);\r\n    toast.info('Starting dental analysis...');\r\n  };\r\n\r\n  const handleImageCaptured = (imageSrc) => {\r\n    setCurrentCapturedImage(imageSrc);\r\n  };\r\n\r\n  const getServerStatusColor = () => {\r\n    switch (serverStatus) {\r\n      case 'connected': return 'text-green-600';\r\n      case 'disconnected': return 'text-red-600';\r\n      case 'error': return 'text-yellow-600';\r\n      default: return 'text-gray-600';\r\n    }\r\n  };\r\n\r\n  const getServerStatusText = () => {\r\n    switch (serverStatus) {\r\n      case 'connected': return 'YOLOv8 Connected';\r\n      case 'disconnected': return 'YOLOv8 Disconnected';\r\n      case 'error': return 'YOLOv8 Error';\r\n      default: return 'Checking...';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isConnected={isConnected} />\r\n\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              {/* Header */}\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Intraoral Patient Dashboard\r\n                  </h1>\r\n                  <p className=\"text-[#333333]\">Real-time dental analysis and consultation</p>\r\n                  <div className=\"flex items-center mt-2\">\r\n                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getServerStatusColor()}`}>\r\n                      <FaServer className=\"mr-1 h-3 w-3\" />\r\n                      {getServerStatusText()}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex flex-col gap-3\">\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    onClick={startAnalysis}\r\n                    disabled={isAnalyzing || serverStatus !== 'connected'}\r\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    <FaBrain className=\"h-5 w-5 mr-2\" />\r\n                    {isAnalyzing ? 'Analyzing...' : 'Start Analysis'}\r\n                  </motion.button>\r\n                  {serverStatus !== 'connected' && (\r\n                    <p className=\"text-xs text-red-600 text-center\">\r\n                      YOLOv8 server required for analysis\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Stats Cards */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.1 }}\r\n                className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\"\r\n              >\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaUser className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Patient</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{patientInfo.name}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaVideo className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Video Status</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{isConnected ? 'Live' : 'Offline'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaEye className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Detections</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{detectionResults.length}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaHistory className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Total Analyses</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{analysisHistory.length}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className={`p-3 rounded-full ${serverStatus === 'connected' ? 'bg-green-100 text-green-600' : serverStatus === 'error' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`}>\r\n                      <FaServer className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">YOLOv8 Server</h2>\r\n                      <p className={`text-2xl font-bold ${getServerStatusColor()}`}>\r\n                        {serverStatus === 'connected' ? 'Online' : serverStatus === 'error' ? 'Error' : 'Offline'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Main Content - Split Layout */}\r\n              <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\">\r\n                {/* Live Video Section - Larger */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaVideo className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Live Video Consultation</h2>\r\n                  </div>\r\n                  <VideoCall\r\n                    onConnectionStatus={handleConnectionStatus}\r\n                    onStartAnalysis={startAnalysis}\r\n                    onImageCaptured={handleImageCaptured}\r\n                    onDetectionResults={handleDetectionResults}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Patient Info Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: 20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaUser className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Patient Information</h2>\r\n                  </div>\r\n                  <PatientInfo patient={patientInfo} />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Analysis and Results Section */}\r\n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\r\n                {/* AI Analysis Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaBrain className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">AI Dental Analysis</h2>\r\n                  </div>\r\n                  <YOLODetection\r\n                    onResults={handleDetectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                    currentImage={currentCapturedImage}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Results Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaChartBar className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Analysis Results</h2>\r\n                  </div>\r\n                  <AnalysisResults\r\n                    results={detectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                    history={analysisHistory}\r\n                  />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Static Patient Summary Section - Always Visible */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.4 }}\r\n                className=\"mt-8\"\r\n              >\r\n                <div className=\"flex items-center mb-6\">\r\n                  <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                    <FaHistory className=\"h-5 w-5\" />\r\n                  </div>\r\n                  <h2 className=\"text-2xl font-bold text-[#0077B6]\">Patient Summary & History</h2>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n                  {/* Summary Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaChartBar className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Current Summary</h3>\r\n                    </div>\r\n                    \r\n                    {detectionResults.length > 0 ? (\r\n                      <div className=\"space-y-4\">\r\n                        <div className=\"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\">\r\n                          <div className=\"flex items-center justify-between mb-2\">\r\n                            <span className=\"text-sm font-medium text-gray-700\">Total Detections</span>\r\n                            <span className=\"text-lg font-bold text-[#0077B6]\">{detectionResults.length}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span className=\"text-sm font-medium text-gray-700\">Primary Issue</span>\r\n                            <span className=\"text-sm font-semibold text-gray-900 capitalize\">\r\n                              {detectionResults[0]?.class.replace('-', ' ') || 'None'}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <div className=\"space-y-2\">\r\n                          {detectionResults.map((result, index) => (\r\n                            <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\r\n                              <span className=\"text-sm font-medium text-gray-700 capitalize\">\r\n                                {result.class.replace('-', ' ')}\r\n                              </span>\r\n                              <span className=\"text-sm font-bold text-[#0077B6]\">\r\n                                {(result.confidence * 100).toFixed(1)}%\r\n                              </span>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaSearch className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No current analysis</p>\r\n                        <p className=\"text-gray-500 text-sm\">Start analysis to see summary</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Recommendations Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaInfoCircle className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recommendations</h3>\r\n                    </div>\r\n                    \r\n                    {detectionResults.length > 0 ? (\r\n                      <div className=\"space-y-3\">\r\n                        {(() => {\r\n                          const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n                          const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n                          const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\r\n                          const recommendations = [];\r\n\r\n                          if (decayCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'urgent',\r\n                              title: 'Immediate Treatment',\r\n                              description: 'Cavities require immediate dental treatment.',\r\n                              icon: <FaTimesCircle className=\"h-4 w-4 text-red-500\" />\r\n                            });\r\n                          }\r\n\r\n                          if (earlyDecayCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'warning',\r\n                              title: 'Preventive Care',\r\n                              description: 'Schedule follow-up for preventive treatment.',\r\n                              icon: <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />\r\n                            });\r\n                          }\r\n\r\n                          if (healthyCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'positive',\r\n                              title: 'Good Oral Health',\r\n                              description: 'Continue regular oral hygiene routine.',\r\n                              icon: <FaCheckCircle className=\"h-4 w-4 text-green-500\" />\r\n                            });\r\n                          }\r\n\r\n                          recommendations.push({\r\n                            type: 'info',\r\n                            title: 'Regular Checkup',\r\n                            description: 'Schedule next checkup within 6 months.',\r\n                            icon: <FaCalendarAlt className=\"h-4 w-4 text-blue-500\" />\r\n                          });\r\n\r\n                          return recommendations.map((rec, index) => (\r\n                            <div key={index} className=\"flex items-start p-3 bg-gray-50 rounded-lg\">\r\n                              <div className=\"mr-3 mt-1\">\r\n                                {rec.icon}\r\n                              </div>\r\n                              <div>\r\n                                <h4 className=\"text-sm font-semibold text-gray-900\">{rec.title}</h4>\r\n                                <p className=\"text-xs text-gray-600\">{rec.description}</p>\r\n                              </div>\r\n                            </div>\r\n                          ));\r\n                        })()}\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaInfoCircle className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No recommendations</p>\r\n                        <p className=\"text-gray-500 text-sm\">Complete analysis for recommendations</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Analysis History Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaHistory className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Analysis History</h3>\r\n                    </div>\r\n                    \r\n                    {analysisHistory.length > 0 ? (\r\n                      <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n                        {analysisHistory.slice(0, 5).map((historyItem, index) => {\r\n                          const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\r\n                          const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\r\n                          const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\r\n                          \r\n                          let severity = 'low';\r\n                          let icon = <FaCheckCircle className=\"h-4 w-4 text-green-500\" />;\r\n                          \r\n                          if (decayCount > 0) {\r\n                            severity = 'high';\r\n                            icon = <FaTimesCircle className=\"h-4 w-4 text-red-500\" />;\r\n                          } else if (earlyDecayCount > 0) {\r\n                            severity = 'medium';\r\n                            icon = <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />;\r\n                          }\r\n\r\n                          return (\r\n                            <div key={historyItem.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                              <div className=\"flex items-center\">\r\n                                <div className=\"mr-3\">\r\n                                  {icon}\r\n                                </div>\r\n                                <div>\r\n                                  <p className=\"text-sm font-medium text-gray-900\">\r\n                                    {historyItem.results.length} detection(s)\r\n                                  </p>\r\n                                  <p className=\"text-xs text-gray-500\">\r\n                                    {new Date(historyItem.timestamp).toLocaleDateString()}\r\n                                  </p>\r\n                                </div>\r\n                              </div>\r\n                              <span className=\"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\">\r\n                                {severity}\r\n                              </span>\r\n                            </div>\r\n                          );\r\n                        })}\r\n                        \r\n                        {analysisHistory.length > 5 && (\r\n                          <div className=\"text-center pt-2\">\r\n                            <p className=\"text-xs text-gray-500\">\r\n                              +{analysisHistory.length - 5} more analyses\r\n                            </p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaHistory className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No analysis history</p>\r\n                        <p className=\"text-gray-500 text-sm\">Perform analyses to build history</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      <ToastContainer\r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop={false}\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme=\"light\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/M,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,UAAU,CAAC;EAC5D,MAAM,CAACqC,WAAW,CAAC,GAAGrC,QAAQ,CAAC;IAC7BsC,IAAI,EAAE,UAAU;IAChBC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMkD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAa,CAAC;MAC3C,IAAID,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCnB,eAAe,CAAC,WAAW,CAAC;QAC5BoB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEH,IAAI,CAAC;MACjD,CAAC,MAAM;QACLlB,eAAe,CAAC,OAAO,CAAC;QACxBoB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdtB,eAAe,CAAC,cAAc,CAAC;MAC/BoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,KAAK,CAACC,OAAO,CAAC;IAC9D;EACF,CAAC;EAED1D,SAAS,CAAC,MAAM;IACdiD,iBAAiB,CAAC,CAAC;IACnB;IACA,MAAMU,QAAQ,GAAGC,WAAW,CAACX,iBAAiB,EAAE,KAAK,CAAC;IACtD,OAAO,MAAMY,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,sBAAsB,GAAIC,MAAM,IAAK;IACzChC,cAAc,CAACgC,MAAM,CAAC;IACtB,IAAIA,MAAM,EAAE;MACV7D,KAAK,CAAC8D,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC,MAAM;MACL9D,KAAK,CAACuD,KAAK,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;EAED,MAAMQ,sBAAsB,GAAGA,CAACC,OAAO,EAAEC,kBAAkB,KAAK;IAC9DzB,mBAAmB,CAACwB,OAAO,CAAC;IAC5BtB,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,MAAMwB,WAAW,GAAG;MAClB9B,EAAE,EAAE+B,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCN,OAAO,EAAEA,OAAO;MAChBO,KAAK,EAAE5B,oBAAoB;MAC3B6B,cAAc,EAAEP,kBAAkB;MAClCQ,SAAS,EAAEvC,WAAW,CAACE,EAAE;MACzBsC,WAAW,EAAExC,WAAW,CAACC;IAC3B,CAAC;IAEDW,kBAAkB,CAAC6B,IAAI,IAAI,CAACT,WAAW,EAAE,GAAGS,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjE,IAAIZ,OAAO,CAACa,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,UAAU,GAAGd,OAAO,CAACe,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MACtDjF,KAAK,CAACkF,IAAI,CAAC,aAAaJ,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIpD,YAAY,KAAK,WAAW,EAAE;MAChChC,KAAK,CAACuD,KAAK,CAAC,qEAAqE,CAAC;MAClF;IACF;IAEAb,cAAc,CAAC,IAAI,CAAC;IACpB1C,KAAK,CAACkF,IAAI,CAAC,6BAA6B,CAAC;EAC3C,CAAC;EAED,MAAMG,mBAAmB,GAAIC,QAAQ,IAAK;IACxC1C,uBAAuB,CAAC0C,QAAQ,CAAC;EACnC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQvD,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,gBAAgB;MACzC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,OAAO;QAAE,OAAO,iBAAiB;MACtC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAMwD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQxD,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,cAAc;QAAE,OAAO,qBAAqB;MACjD,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC;QAAS,OAAO,aAAa;IAC/B;EACF,CAAC;EAED,oBACER,OAAA;IAAKiE,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvClE,OAAA,CAACH,OAAO;MAACsE,MAAM,EAAE7D,WAAY;MAAC8D,SAAS,EAAE7D;IAAe;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3DxE,OAAA;MAAKiE,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDlE,OAAA,CAACF,MAAM;QAAC2E,aAAa,EAAEA,CAAA,KAAMlE,cAAc,CAAC,CAACD,WAAW,CAAE;QAACF,WAAW,EAAEA;MAAY;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvFxE,OAAA;QAAMiE,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGlE,OAAA;UAAKiE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChClE,OAAA,CAACvB,MAAM,CAACiG,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAG9BlE,OAAA;cAAKiE,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FlE,OAAA;gBAAAkE,QAAA,gBACElE,OAAA;kBAAIiE,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxE,OAAA;kBAAGiE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5ExE,OAAA;kBAAKiE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrClE,OAAA;oBAAMiE,SAAS,EAAE,uEAAuEF,oBAAoB,CAAC,CAAC,EAAG;oBAAAG,QAAA,gBAC/GlE,OAAA,CAACR,QAAQ;sBAACyE,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACpCR,mBAAmB,CAAC,CAAC;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxE,OAAA;gBAAKiE,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClClE,OAAA,CAACvB,MAAM,CAACuG,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAExB,aAAc;kBACvByB,QAAQ,EAAEpE,WAAW,IAAIT,YAAY,KAAK,WAAY;kBACtDyD,SAAS,EAAC,oPAAoP;kBAAAC,QAAA,gBAE9PlE,OAAA,CAAClB,OAAO;oBAACmF,SAAS,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCvD,WAAW,GAAG,cAAc,GAAG,gBAAgB;gBAAA;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACfhE,YAAY,KAAK,WAAW,iBAC3BR,OAAA;kBAAGiE,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEhD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxE,OAAA,CAACvB,MAAM,CAACiG,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErElE,OAAA;gBAAKiE,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClE,OAAA;oBAAKiE,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElE,OAAA,CAACpB,MAAM;sBAACqF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNxE,OAAA;oBAAKiE,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlE,OAAA;sBAAIiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9DxE,OAAA;sBAAGiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAExD,WAAW,CAACC;oBAAI;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxE,OAAA;gBAAKiE,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClE,OAAA;oBAAKiE,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElE,OAAA,CAACrB,OAAO;sBAACsF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNxE,OAAA;oBAAKiE,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlE,OAAA;sBAAIiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnExE,OAAA;sBAAGiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE9D,WAAW,GAAG,MAAM,GAAG;oBAAS;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxE,OAAA;gBAAKiE,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClE,OAAA;oBAAKiE,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElE,OAAA,CAAChB,KAAK;sBAACiF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACNxE,OAAA;oBAAKiE,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlE,OAAA;sBAAIiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjExE,OAAA;sBAAGiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEnD,gBAAgB,CAACsC;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxE,OAAA;gBAAKiE,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClE,OAAA;oBAAKiE,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElE,OAAA,CAACd,SAAS;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACNxE,OAAA;oBAAKiE,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlE,OAAA;sBAAIiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrExE,OAAA;sBAAGiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE7C,eAAe,CAACgC;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxE,OAAA;gBAAKiE,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlE,OAAA;kBAAKiE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClE,OAAA;oBAAKiE,SAAS,EAAE,oBAAoBzD,YAAY,KAAK,WAAW,GAAG,6BAA6B,GAAGA,YAAY,KAAK,OAAO,GAAG,+BAA+B,GAAG,yBAAyB,EAAG;oBAAA0D,QAAA,eAC1LlE,OAAA,CAACR,QAAQ;sBAACyE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACNxE,OAAA;oBAAKiE,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlE,OAAA;sBAAIiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpExE,OAAA;sBAAGiE,SAAS,EAAE,sBAAsBF,oBAAoB,CAAC,CAAC,EAAG;sBAAAG,QAAA,EAC1D1D,YAAY,KAAK,WAAW,GAAG,QAAQ,GAAGA,YAAY,KAAK,OAAO,GAAG,OAAO,GAAG;oBAAS;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbxE,OAAA;cAAKiE,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDlE,OAAA,CAACvB,MAAM,CAACiG,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,iIAAiI;gBAAAC,QAAA,gBAE3IlE,OAAA;kBAAKiE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClE,OAAA;oBAAKiE,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElE,OAAA,CAACrB,OAAO;sBAACsF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNxE,OAAA;oBAAIiE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNxE,OAAA,CAACP,SAAS;kBACRgG,kBAAkB,EAAErD,sBAAuB;kBAC3CsD,eAAe,EAAE9B,aAAc;kBAC/B+B,eAAe,EAAE9B,mBAAoB;kBACrC+B,kBAAkB,EAAErD;gBAAuB;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbxE,OAAA,CAACvB,MAAM,CAACiG,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAG,CAAE;gBAC/BX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HlE,OAAA;kBAAKiE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClE,OAAA;oBAAKiE,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElE,OAAA,CAACpB,MAAM;sBAACqF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNxE,OAAA;oBAAIiE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNxE,OAAA,CAACL,WAAW;kBAACkG,OAAO,EAAEnF;gBAAY;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNxE,OAAA;cAAKiE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDlE,OAAA,CAACvB,MAAM,CAACiG,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HlE,OAAA;kBAAKiE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClE,OAAA;oBAAKiE,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElE,OAAA,CAAClB,OAAO;sBAACmF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNxE,OAAA;oBAAIiE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNxE,OAAA,CAACN,aAAa;kBACZoG,SAAS,EAAEvD,sBAAuB;kBAClCtB,WAAW,EAAEA,WAAY;kBACzB8E,YAAY,EAAE5E;gBAAqB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbxE,OAAA,CAACvB,MAAM,CAACiG,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HlE,OAAA;kBAAKiE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClE,OAAA;oBAAKiE,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElE,OAAA,CAACnB,UAAU;sBAACoF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNxE,OAAA;oBAAIiE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNxE,OAAA,CAACJ,eAAe;kBACd4C,OAAO,EAAEzB,gBAAiB;kBAC1BE,WAAW,EAAEA,WAAY;kBACzB+E,OAAO,EAAE3E;gBAAgB;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNxE,OAAA,CAACvB,MAAM,CAACiG,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhBlE,OAAA;gBAAKiE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrClE,OAAA;kBAAKiE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,eAC1ElE,OAAA,CAACd,SAAS;oBAAC+E,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACNxE,OAAA;kBAAIiE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eAENxE,OAAA;gBAAKiE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAEpDlE,OAAA;kBAAKiE,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIlE,OAAA;oBAAKiE,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrClE,OAAA;sBAAKiE,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1ElE,OAAA,CAACnB,UAAU;wBAACoF,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACNxE,OAAA;sBAAIiE,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAELzD,gBAAgB,CAACsC,MAAM,GAAG,CAAC,gBAC1BrD,OAAA;oBAAKiE,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBlE,OAAA;sBAAKiE,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,gBACvDlE,OAAA;wBAAKiE,SAAS,EAAC,wCAAwC;wBAAAC,QAAA,gBACrDlE,OAAA;0BAAMiE,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC3ExE,OAAA;0BAAMiE,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,EAAEnD,gBAAgB,CAACsC;wBAAM;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC,eACNxE,OAAA;wBAAKiE,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDlE,OAAA;0BAAMiE,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAa;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxExE,OAAA;0BAAMiE,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,EAC7D,EAAA/D,kBAAA,GAAAY,gBAAgB,CAAC,CAAC,CAAC,cAAAZ,kBAAA,uBAAnBA,kBAAA,CAAqBsD,KAAK,CAACwC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI;wBAAM;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENxE,OAAA;sBAAKiE,SAAS,EAAC,WAAW;sBAAAC,QAAA,EACvBnD,gBAAgB,CAACwC,GAAG,CAAC,CAACC,MAAM,EAAE0C,KAAK,kBAClClG,OAAA;wBAAiBiE,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACnFlE,OAAA;0BAAMiE,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,EAC3DV,MAAM,CAACC,KAAK,CAACwC,OAAO,CAAC,GAAG,EAAE,GAAG;wBAAC;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACPxE,OAAA;0BAAMiE,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,GAC/C,CAACV,MAAM,CAAC2C,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;wBAAA;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,GANC0B,KAAK;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOV,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAENxE,OAAA;oBAAKiE,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlE,OAAA,CAACf,QAAQ;sBAACgF,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7DxE,OAAA;sBAAGiE,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrExE,OAAA;sBAAGiE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNxE,OAAA;kBAAKiE,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIlE,OAAA;oBAAKiE,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrClE,OAAA;sBAAKiE,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1ElE,OAAA,CAACb,YAAY;wBAAC8E,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACNxE,OAAA;sBAAIiE,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAELzD,gBAAgB,CAACsC,MAAM,GAAG,CAAC,gBAC1BrD,OAAA;oBAAKiE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACvB,CAAC,MAAM;sBACN,MAAMmC,UAAU,GAAGtF,gBAAgB,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACjF,MAAMmD,eAAe,GAAGzF,gBAAgB,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACtF,MAAMoD,YAAY,GAAG1F,gBAAgB,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,eAAe,CAAC,CAACJ,MAAM;sBACrF,MAAMqD,eAAe,GAAG,EAAE;sBAE1B,IAAIL,UAAU,GAAG,CAAC,EAAE;wBAClBK,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,QAAQ;0BACdC,KAAK,EAAE,qBAAqB;0BAC5BC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE/G,OAAA,CAACZ,aAAa;4BAAC6E,SAAS,EAAC;0BAAsB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACzD,CAAC,CAAC;sBACJ;sBAEA,IAAIgC,eAAe,GAAG,CAAC,EAAE;wBACvBE,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,iBAAiB;0BACxBC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE/G,OAAA,CAACX,qBAAqB;4BAAC4E,SAAS,EAAC;0BAAyB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACpE,CAAC,CAAC;sBACJ;sBAEA,IAAIiC,YAAY,GAAG,CAAC,EAAE;wBACpBC,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,UAAU;0BAChBC,KAAK,EAAE,kBAAkB;0BACzBC,WAAW,EAAE,wCAAwC;0BACrDC,IAAI,eAAE/G,OAAA,CAACV,aAAa;4BAAC2E,SAAS,EAAC;0BAAwB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAC3D,CAAC,CAAC;sBACJ;sBAEAkC,eAAe,CAACC,IAAI,CAAC;wBACnBC,IAAI,EAAE,MAAM;wBACZC,KAAK,EAAE,iBAAiB;wBACxBC,WAAW,EAAE,wCAAwC;wBACrDC,IAAI,eAAE/G,OAAA,CAACT,aAAa;0BAAC0E,SAAS,EAAC;wBAAuB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAC1D,CAAC,CAAC;sBAEF,OAAOkC,eAAe,CAACnD,GAAG,CAAC,CAACyD,GAAG,EAAEd,KAAK,kBACpClG,OAAA;wBAAiBiE,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACrElE,OAAA;0BAAKiE,SAAS,EAAC,WAAW;0BAAAC,QAAA,EACvB8C,GAAG,CAACD;wBAAI;0BAAA1C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC,eACNxE,OAAA;0BAAAkE,QAAA,gBACElE,OAAA;4BAAIiE,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAE8C,GAAG,CAACH;0BAAK;4BAAAxC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpExE,OAAA;4BAAGiE,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAE8C,GAAG,CAACF;0BAAW;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD,CAAC;sBAAA,GAPE0B,KAAK;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQV,CACN,CAAC;oBACJ,CAAC,EAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,gBAENxE,OAAA;oBAAKiE,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlE,OAAA,CAACb,YAAY;sBAAC8E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjExE,OAAA;sBAAGiE,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpExE,OAAA;sBAAGiE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAqC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNxE,OAAA;kBAAKiE,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIlE,OAAA;oBAAKiE,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrClE,OAAA;sBAAKiE,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1ElE,OAAA,CAACd,SAAS;wBAAC+E,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACNxE,OAAA;sBAAIiE,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,EAELnD,eAAe,CAACgC,MAAM,GAAG,CAAC,gBACzBrD,OAAA;oBAAKiE,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAChD7C,eAAe,CAAC+B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACG,GAAG,CAAC,CAAC0D,WAAW,EAAEf,KAAK,KAAK;sBACvD,MAAMG,UAAU,GAAGY,WAAW,CAACzE,OAAO,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACpF,MAAMmD,eAAe,GAAGS,WAAW,CAACzE,OAAO,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACzF,MAAMoD,YAAY,GAAGQ,WAAW,CAACzE,OAAO,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,eAAe,CAAC,CAACJ,MAAM;sBAExF,IAAI6D,QAAQ,GAAG,KAAK;sBACpB,IAAIH,IAAI,gBAAG/G,OAAA,CAACV,aAAa;wBAAC2E,SAAS,EAAC;sBAAwB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;sBAE/D,IAAI6B,UAAU,GAAG,CAAC,EAAE;wBAClBa,QAAQ,GAAG,MAAM;wBACjBH,IAAI,gBAAG/G,OAAA,CAACZ,aAAa;0BAAC6E,SAAS,EAAC;wBAAsB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAC3D,CAAC,MAAM,IAAIgC,eAAe,GAAG,CAAC,EAAE;wBAC9BU,QAAQ,GAAG,QAAQ;wBACnBH,IAAI,gBAAG/G,OAAA,CAACX,qBAAqB;0BAAC4E,SAAS,EAAC;wBAAyB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBACtE;sBAEA,oBACExE,OAAA;wBAA0BiE,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,gBAC/FlE,OAAA;0BAAKiE,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChClE,OAAA;4BAAKiE,SAAS,EAAC,MAAM;4BAAAC,QAAA,EAClB6C;0BAAI;4BAAA1C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACNxE,OAAA;4BAAAkE,QAAA,gBACElE,OAAA;8BAAGiE,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,GAC7C+C,WAAW,CAACzE,OAAO,CAACa,MAAM,EAAC,eAC9B;4BAAA;8BAAAgB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC,eACJxE,OAAA;8BAAGiE,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EACjC,IAAIvB,IAAI,CAACsE,WAAW,CAACpE,SAAS,CAAC,CAACsE,kBAAkB,CAAC;4BAAC;8BAAA9C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNxE,OAAA;0BAAMiE,SAAS,EAAC,oFAAoF;0BAAAC,QAAA,EACjGgD;wBAAQ;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,GAhBCyC,WAAW,CAACrG,EAAE;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiBnB,CAAC;oBAEV,CAAC,CAAC,EAEDnD,eAAe,CAACgC,MAAM,GAAG,CAAC,iBACzBrD,OAAA;sBAAKiE,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BlE,OAAA;wBAAGiE,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAAC7C,eAAe,CAACgC,MAAM,GAAG,CAAC,EAAC,gBAC/B;sBAAA;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENxE,OAAA;oBAAKiE,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlE,OAAA,CAACd,SAAS;sBAAC+E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9DxE,OAAA;sBAAGiE,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrExE,OAAA;sBAAGiE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAiC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENxE,OAAA,CAACzB,cAAc;MACb6I,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACtE,EAAA,CA5gBQD,GAAG;AAAA6H,EAAA,GAAH7H,GAAG;AA8gBZ,eAAeA,GAAG;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}